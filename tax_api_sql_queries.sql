-- SQL Queries executed by Tax V3 API for your payload
-- Hotel ID: 1438960, Category: roomnight, Price: 1750.00

-- 1. Get Hotel Configuration and State Mapping
-- This query fetches hotel details and maps hotel to state
SELECT 
    hc.id, hc.hotel_id, hc.hx_id, hc.cs_hotel_id, hc.state_id, 
    hc.state_identifier, hc.hotel_name, hc.hotel_luxury_tax_enabled,
    hc.arr_tax_enabled, hc.declared_tariff_enabled, 
    hc.use_arr_for_tax_calculation, hc.churned
FROM taxation_hotel_config hc 
WHERE hc.cs_hotel_id IN ('1438960');

-- 2. Get Service Details for Category
-- This query fetches service information for the 'roomnight' category
SELECT 
    s.id, s.name, s.service_accounting_code, s.category_id, 
    s.declared_tariff_required
FROM taxation_service s 
WHERE s.category_id IN ('roomnight');

-- 3. Get CGST Configuration
-- This query fetches Central GST rates for the service
SELECT 
    c.id, c.service_name, c.service_id, c.from_price_pretax, 
    c.to_price_pretax, c.from_price_posttax, c.to_price_posttax,
    c.effective_date, c.expiry_date, c.cgst_percent
FROM taxation_cgst c 
WHERE c.service_id IN (
    SELECT s.id FROM taxation_service s WHERE s.category_id = 'roomnight'
)
AND c.effective_date <= '2025-09-23' 
AND c.expiry_date >= '2025-09-23'
AND c.from_price_pretax <= 1750.00 
AND c.to_price_pretax >= 1750.00;

-- 4. Get SGST Configuration  
-- This query fetches State GST rates for the hotel's state and service
SELECT 
    sg.id, sg.service_name, sg.service_id, sg.state_id, sg.state_identifier,
    sg.from_price_pretax, sg.to_price_pretax, sg.from_price_posttax, 
    sg.to_price_posttax, sg.effective_date, sg.expiry_date, 
    sg.sgst_percent, sg.gst_percent
FROM taxation_sgst sg 
WHERE sg.service_id IN (
    SELECT s.id FROM taxation_service s WHERE s.category_id = 'roomnight'
)
AND sg.state_id IN (
    SELECT hc.state_id FROM taxation_hotel_config hc WHERE hc.cs_hotel_id = '1438960'
)
AND sg.effective_date <= '2025-09-23' 
AND sg.expiry_date >= '2025-09-23'
AND sg.from_price_pretax <= 1750.00 
AND sg.to_price_pretax >= 1750.00;

-- 5. Get Other Tax Configuration (if applicable for the state)
-- This query fetches additional taxes like luxury tax, etc.
SELECT 
    ot.id, ot.service_name, ot.service_id, ot.state_id, ot.state_identifier,
    ot.from_price_pretax, ot.to_price_pretax, ot.from_price_posttax,
    ot.to_price_posttax, ot.effective_date, ot.expiry_date, 
    ot.tax_percent, ot.tax_type_id, ot.tax_type_name
FROM taxation_other_tax ot 
WHERE ot.service_id IN (
    SELECT s.id FROM taxation_service s WHERE s.category_id = 'roomnight'
)
AND ot.state_id IN (
    SELECT hc.state_id FROM taxation_hotel_config hc WHERE hc.cs_hotel_id = '1438960'
)
AND ot.effective_date <= '2025-09-23' 
AND ot.expiry_date >= '2025-09-23'
AND ot.from_price_pretax <= 1750.00 
AND ot.to_price_pretax >= 1750.00;

-- 6. Get State Information
-- This query fetches state details if needed
SELECT 
    st.id, st.state_id, st.cs_state_id, st.state_name
FROM taxation_state st 
WHERE st.state_id IN (
    SELECT hc.state_id FROM taxation_hotel_config hc WHERE hc.cs_hotel_id = '1438960'
);

-- Note: The actual implementation uses Redis caching extensively, so these queries 
-- may not hit the database directly if the data is already cached.
-- The cache keys used are:
-- - hotel_configs_cs_id for hotel configurations
-- - service_codes for service mappings  
-- - cgst_id for CGST rates
-- - sgst for SGST rates
-- - other_tax_key for other tax rates
