version: "2.1"

services:
  tax-db:
    image: "postgres:9.6.5"
    container_name: "${DB_HOST}"
    hostname: "${DB_HOST}"
    environment:
      POSTGRES_USER: "${DB_USER}"
      POSTGRES_PASSWORD: "${DB_PASSWORD}"
      POSTGRES_DB: "${DB_NAME}"
    volumes:
      - "${POSTGRES_DATA_PATH}:/var/lib/postgresql"
      - ${repo}/envrepo/taxation/prod-tax-schema.sql:/docker-entrypoint-initdb.d/0-schema.sql
    ports:
      - "${DB_PORT}:5432"
    tty: true
  redis:
    image: redis
    container_name: "${REDIS_HOST}"
    hostname: "${REDIS_HOST}"
    ports:
      - "6378:6379"
  rmq:
    image: "docker-hub.treebo.com:5000/rmq_curl"
    container_name: "${RMQ_CONTAINER_NAME}"
    environment:
      RABBITMQ_DEFAULT_USER: "${RMQ_USER}"
      RABBITMQ_DEFAULT_PASS: "${RMQ_PWD}"
    ports:
      - "15672:15672"
      - "5672:5672"
    labels:
      NAME: "rmq1"
    tty: true