version: "2.1"

services:
  tax-service:
    ports:
      - "${HOST_PORT}:8001"
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container
    entrypoint: /usr/src/app/gunicorn_start_local.sh
    container_name: "taxation_gunicorn"
    depends_on:
      tax-db:
        condition: service_healthy
      redis:
        condition: service_healthy

  tax-cataloging-hotel-sync-consumer-service:
    container_name: "tax_cataloging_hotel_sync_consumer_service"
    command: flask event_consumer --consumer=taxation.consumer.cs_consumer.CSHotelSyncConsumer
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container

#  catalog-service-room-consumer:
#    container_name: "tax_cs_room_sync"
#    command: flask event_consumer --consumer=taxation.consumer.cs_consumer.CSRoomSyncConsumer
#    extends:
#      file: base-compose.yml
#      service: tax_service_base_container

  tax-cataloging-category-sync-consumer-service:
    container_name: "tax_cataloging_category_sync_consumer_service"
    command: flask event_consumer --consumer=taxation.consumer.cs_consumer.CSServiceSyncConsumer
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container

  rmq:
    extends:
      file: infra-compose.yml
      service: rmq
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:15672"]
      interval: 10s
      timeout: 15s
      retries: 3

  tax-db:
    extends:
      file: infra-compose.yml
      service: tax-db
    volumes:
      - ${repo}/envrepo/taxation/prod-tax-data.sql:/docker-entrypoint-initdb.d/1-data.sql
    healthcheck:
      test: "exit 0"
      interval: 10s
      timeout: 15s
      retries: 3

  redis:
    extends:
      file: infra-compose.yml
      service: redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 15s
      retries: 3
