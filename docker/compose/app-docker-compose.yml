version: "2.1"

services:
  taxation-app-service:
    ports:
      - "${HOST_PORT}:8001"
    volumes:
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${HOST_DOC_ROOT}:/usr/src/doc"
    container_name: "taxation_app_server"
    restart: always
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container
    entrypoint: /usr/src/app/gunicorn_start
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}