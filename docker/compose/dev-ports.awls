secgroup = create securitygroup vpc={vpc_id} description={description} name={group_name}
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=*************/32 portrange=80
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=*************/32 portrange=443
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=*************/32 portrange=80
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=*************/32 portrange=443
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=************/32 portrange=80
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=************/32 portrange=443
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=***************/32 portrange=80
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=***************/32 portrange=443
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=***********/16 portrange=80
update securitygroup id=$secgroup inbound=authorize protocol=tcp cidr=***********/16 portrange=443
