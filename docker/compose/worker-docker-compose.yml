version: "2.1"

services:
  tax-catalog-consumer:
    container_name: "tax_catalog_consumer_${TENANT_ID}"
    command: flask start_catalog_consumer --tenant_id=$TENANT_ID
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container
    restart: always
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}