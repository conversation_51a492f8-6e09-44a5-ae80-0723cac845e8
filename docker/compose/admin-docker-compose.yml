version: "2.1"

services:
  taxation-service-admin:
    ports:
      - "${HOST_PORT}:8001"
    volumes:
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${HOST_DOC_ROOT}:/usr/src/doc"
    container_name: "taxation_admin_${ADMIN_TENANT_ID}"
    environment:
      - ADMIN_TENANT_ID
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}
      - GUNICORN_WORKERS=2
    restart: always
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container
    entrypoint: /usr/src/app/gunicorn_start
