version: "2.1"

services:
  tax-service:
    ports:
      - "${HOST_PORT}:8001"
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container
    entrypoint: /usr/src/app/gunicorn_start
    container_name: "taxation_gunicorn"

  tax-cataloging-hotel-sync-consumer-service:
    container_name: "tax_cataloging_hotel_sync_consumer_service"
    command: flask event_consumer --consumer=taxation.consumer.cs_consumer.CSHotelSyncConsumer
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container

#  catalog-service-room-consumer:
#    container_name: "tax_cs_room_sync"
#    command: flask event_consumer --consumer=taxation.consumer.cs_consumer.CSRoomSyncConsumer
#    extends:
#      file: base-compose.yml
#      service: tax_service_base_container

  tax-cataloging-category-sync-consumer-service:
    container_name: "tax_cataloging_category_sync_consumer_service"
    command: flask event_consumer --consumer=taxation.consumer.cs_consumer.CSServiceSyncConsumer
    extends:
      file: base-docker-compose.yml
      service: tax_service_base_container
