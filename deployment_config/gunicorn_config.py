import os

from psycogreen.gevent import patch_psycopg

bind = '0.0.0.0:8001'

reload = True
workers = 3 if os.environ.get('APP_ENV') in ('production', 'prod') else 1

keepalive = 30

worker_class = 'gevent'

worker_connections = 10
timeout = 60

# pidfile = '/var/run/taxation_gunicorn.pid'

environment = os.environ.get('APP_ENV', 'local')
log_root = os.environ.get('LOG_ROOT', '.')

logconfig_dict = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': "[%(asctime)s] %(levelname)s %(request_id)s - [%(name)s:%(lineno)s] %(message)s",
            },
            'logstash': {
                '()': 'logstash_formatter.LogstashFormatterV1'
            }
        },
        'handlers': {
            'gunicorn.access': {
                'class': 'logging.handlers.WatchedFileHandler',
                'filename': log_root + '/gunicorn_access.log'
            },
            'gunicorn.error': {
                'class': 'logging.handlers.WatchedFileHandler',
                'filename': log_root + '/gunicorn_error.log'
            },
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'logstash'
            }
        },
        'loggers': {
            'gunicorn.access': {
                'handlers': ['console', 'gunicorn.access'],
                'level': 'INFO' if environment == 'production' else 'DEBUG',
                'propagate': False,
                'qualname': 'gunicorn.access'
            },
            'gunicorn.error': {
                'handlers': ['console', 'gunicorn.error'],
                'level': 'ERROR',
                'propagate': False,
                'qualname': 'gunicorn.error'
            }
        }
    }

proc_name = 'taxation'


def post_fork(server, worker):
    # patch psycopg2 for gevent compatibility
    patch_psycopg()
