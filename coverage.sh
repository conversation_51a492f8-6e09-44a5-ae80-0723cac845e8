#!/bin/bash
# Runs nosetests, and generates coverage report at htmlcov/ directory under current directory
# Access by running: python3 -m http.server 8000, and go to: localhost:8000/htmlcov
# nosetests -v --with-coverage --cover-package=taxation --cover-erase --cover-html --cover-html-dir=htmlcov/
coverage run --source='taxation/' --omit=*/app.py,*/tax_api*,*/exception_handler*,*/admin_views*,*/settings.py,*/serializers*,*/migrations/*,*/tests/*,*/tests*,*/repositories.py -m nose
coverage html
coverage xml
