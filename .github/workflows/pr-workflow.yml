name: release/main PR Workflow

on:
  push:
    branches: [ release, main ]
  pull_request:
    branches: [ release, main ]
  workflow_dispatch:

jobs:
  build:

    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres
        env:
          POSTGRES_DB: taxation_test
          POSTGRES_USER: taxation_user
          POSTGRES_PASSWORD: taxation_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis
        env:
          REDIS_HOST: localhost
          REDIS_PORT: 6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v2
    - uses: webfactory/ssh-agent@v0.5.4
      with:
        ssh-private-key: |
            ${{ secrets.SSH_TREEBO_COMMONS_PRIVATE_KEY }}
            ${{ secrets.SSH_FLASKHEALTHCHECK_PRIVATE_KEY }}

    - name: Set up Python 3.5.10
      run: |
        export  PYENV_ROOT=$HOME/.pyenv
        export  PYENV_SHELL=bash
        export  PATH="$PYENV_ROOT/shims:$PYENV_ROOT/bin:$PATH"
        export  PYTHON_CONFIGURE_OPTS="--enable-shared"
        curl -s -S -L https://raw.githubusercontent.com/pyenv/pyenv-installer/master/bin/pyenv-installer | bash ;
        if command -v pyenv 1>/dev/null 2>&1; then \
            pyenv install 3.5.10 ; \
            pyenv global 3.5.10 ;
        fi ;
                
#      uses: actions/setup-python@v2
#      with:
#        python-version: '3.5.10'

    - name: Install dependencies
      run: |
        set -x
        export  PYENV_SHELL=bash
        export  PYENV_ROOT=$HOME/.pyenv
        export  PATH="$PYENV_ROOT/shims:$PYENV_ROOT/bin:$PATH"
        pip --version

        pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip==18.1
        pip --version
        pip config set global.trusted-host \
            "pypi.org files.pythonhosted.org pypi.python.org" \
            --trusted-host=pypi.python.org \
            --trusted-host=pypi.org \
            --trusted-host=files.pythonhosted.org
        cat << EOF > requirements/deploy.txt
        git+ssh://**************/treebo-noss/flaskhealthcheck.git@main#egg=flaskhealthcheck
        git+ssh://**************/treebo-noss/treebo-common.git@v3.0.0rc1#egg=treebo-commons
        -r base.txt
        EOF
        pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements/dev.txt
        pip install psycopg2-binary 

    - name: Integration Tests with Pytest
      run: |
        export  PYENV_ROOT=$HOME/.pyenv
        export  PATH="$PYENV_ROOT/shims:$PYENV_ROOT/bin:$PATH"
        pytest -x taxation/integration_tests/tests/
