#!/bin/bash
set -x

#1 env(environment), 2 version(docker image versioning), 3 app(application name),
#4 config_branch(used only in the dev-environment), 5 deployment type: app/worker/cron
#environment possible values: local, dev, staging, production
export ENV="$1";export tenant_service_url="${7}"
export VERSION="$2"
export APP="$3"
export branch="$4"
export deployment_type="$5"
export regions="$6"
export CLUSTER_IDENTIFIER="$8"
export AWS_SECRET_PREFIX="$9"

config_file=''
ENV_FILE=''

echo $ENV
echo $VERSION
echo $APP
echo $BRANCH


TREEBO_TENANT_ID="treebo"
allTenants=()

function loadActiveTenants {
  # Read Tenant Ids from TenantGateway
  echo "Loading active tenants"
  if [ "$ENV" == "staging" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")

  elif [ "$ENV" == "production" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  fi

  allTenants=($(echo "$active_tenants" | jq -r '.data[].tenant_id'))
  echo $nonTreeboTenants
}

loadActiveTenants

echo "Tenants loaded: ${allTenants[@]}"
echo "Deploying $APP app on $ENV environment"

setup()
{
    echo $@
    ENV_FILE=${repo}/envrepo/taxation/environment/$@.env
    export ENV_FILE=${ENV_FILE}
    export $(egrep -v '^#' ${ENV_FILE} | xargs)

    # set docker_tagname
    export DOCKER_TAGNAME=${POD_NAME}_$@_${APP}
    echo DOCKER_TAGNAME
    if [ "$deployment_type" == "app" ]; then
        echo "Running app server container"
        docker-compose -f ${repo}/docker/compose/app-docker-compose.yml up -d
        docker-compose -f ${repo}/docker/compose/nginx-docker-compose.yml -p taxation_nginx_service up -d

        echo "Running worker and admin containers for each tenants"
        for tenant_id in ${allTenants[@]}; do
            export ADMIN_TENANT_ID=$tenant_id
            export TENANT_ID=$tenant_id
            export HOST_PORT=$((HOST_PORT + 1))
            # Run worker container on a different port for all tenants
            docker-compose -f ${repo}/docker/compose/worker-docker-compose.yml -p taxation_${TENANT_ID} up -d
            # Run app container on a different port for non tenants -> to run admin
            if [ "$tenant_id" != "$TREEBO_TENANT_ID" ]; then
                docker-compose -f ${repo}/docker/compose/admin-docker-compose.yml -p taxation_${ADMIN_TENANT_ID} up -d
            fi
        done
    fi
}

if [ ${ENV} == "local" ]; then
   export repo=$HOME/${APP}
else
   export repo=/opt/${APP}
fi


if [ "$ENV" == "local" ]; then
    docker-compose -f docker/compose/dev-compose.yml up --build
elif [ "$ENV" == "dev" ]; then
    rm -rf /opt/$APP/envrepo
    <NAME_EMAIL>:treebo/rms-envconfig.git --branch ${branch} /opt/$APP/envrepo

    lsof -i :80 | awk '{print $2}' | grep -v 'PID' | xargs -I {} kill -9 {}
    ENV_FILE=${repo}/envrepo/taxation/environment/$ENV.env
    export ENV_FILE=${ENV_FILE}
    export $(egrep -v '^#' ${ENV_FILE} | xargs)
    export DOCKER_TAGNAME=${POD_NAME}_${ENV}_${APP}
    docker-compose -f ${repo}/docker/compose/nginx-docker-compose.yml up -d
    docker-compose -f ${repo}/docker/compose/dev-compose.yml up -d
elif [ "$ENV" == "staging" ]; then
    echo "staging env"
    setup $ENV
elif [ "$ENV" == "production" ]; then
    echo "production env"
    setup $ENV
fi
