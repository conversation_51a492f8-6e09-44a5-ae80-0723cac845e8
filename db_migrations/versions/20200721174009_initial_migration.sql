-- revision: '20200721174009_initial_migration'
-- down_revision: ''

-- upgrade

--
-- Name: arr_tax_pretax_price_selector; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE arr_tax_pretax_price_selector AS ENUM (
    'Always',
    'RoomPrice lt ARR',
    'RoomPrice gt ARR'
);


--
-- Name: config_data_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE config_data_type AS ENUM (
    'text',
    'int',
    'float',
    'boolean',
    'date',
    'time',
    'timestamp'
);


--
-- Name: room_codes_new; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE room_codes_new AS ENUM (
    'acacia',
    'oak',
    'maple',
    'mahogany'
);


--
-- Name: room_type_codes; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE room_type_codes AS ENUM (
    'acacia',
    'oak',
    'maple',
    'mahogany'
);


--
-- Name: sku_room_codes; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE sku_room_codes AS ENUM (
    'acacia',
    'oak',
    'maple',
    'mahogany'
);


--
-- Name: status_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE status_type AS ENUM (
    'active',
    'inactive',
    'deleted'
);


--
-- Name: tax_types; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE tax_types AS ENUM (
    'ServiceTax',
    'SwatchBharatCess',
    'KrishiKalyanCess'
);


--
-- Name: taxation_arr_luxurytax; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_arr_luxurytax (
    id integer NOT NULL,
    hotel_id integer NOT NULL,
    room_type_code character varying(255) NOT NULL,
    occupancy integer NOT NULL,
    average_room_rate numeric(19,4) NOT NULL,
    effective_date date NOT NULL,
    expiry_date date NOT NULL,
    tax numeric(19,4) NOT NULL
);


--
-- Name: taxation_arr_luxurytax_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_arr_luxurytax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_arr_luxurytax_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_arr_luxurytax_id_seq OWNED BY taxation_arr_luxurytax.id;


--
-- Name: taxation_cgst; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_cgst (
    id integer NOT NULL,
    service_name character varying(8000) NOT NULL,
    from_price_pretax numeric(19,4) NOT NULL,
    to_price_pretax numeric(19,4) NOT NULL,
    from_price_posttax numeric(19,4),
    to_price_posttax numeric(19,4),
    effective_date date NOT NULL,
    expiry_date date NOT NULL,
    cgst_percent numeric(19,4) NOT NULL,
    service_id integer NOT NULL
);


--
-- Name: taxation_cgst_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_cgst_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_cgst_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_cgst_id_seq OWNED BY taxation_cgst.id;


--
-- Name: taxation_declared_tariff; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_declared_tariff (
    id integer NOT NULL,
    created_at timestamp(6) without time zone,
    modified_at timestamp(6) without time zone NOT NULL,
    hotel_id integer,
    room_type_code character varying(255) NOT NULL,
    occupancy integer NOT NULL,
    from_date date NOT NULL,
    to_date date NOT NULL,
    declared_tariff numeric(19,4) NOT NULL,
    hotel_identifier integer NOT NULL,
    cs_hotel_id character varying(8000)
);


--
-- Name: taxation_declared_tariff_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_declared_tariff_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_declared_tariff_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_declared_tariff_id_seq OWNED BY taxation_declared_tariff.id;


--
-- Name: taxation_dt_packet; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_dt_packet (
    id integer NOT NULL,
    created_at timestamp(6) without time zone,
    modified_at timestamp(6) without time zone,
    filename character varying(8000) NOT NULL,
    packet_id integer NOT NULL,
    data character varying(8000) NOT NULL,
    hotel_count integer NOT NULL,
    room_type_count character varying(8000) NOT NULL,
    status character varying(8000) NOT NULL,
    payload character varying(8000)
);


--
-- Name: taxation_dt_packet_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_dt_packet_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_dt_packet_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_dt_packet_id_seq OWNED BY taxation_dt_packet.id;


--
-- Name: taxation_global_config; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_global_config (
    id integer NOT NULL,
    created_at timestamp(6) without time zone,
    modified_at timestamp(6) without time zone NOT NULL,
    config_data_type character varying(255) NOT NULL,
    config_name character varying(8000) NOT NULL,
    config_value character varying(8000) NOT NULL,
    status character varying(255) NOT NULL
);


--
-- Name: taxation_global_config_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_global_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_global_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_global_config_id_seq OWNED BY taxation_global_config.id;


--
-- Name: taxation_hotel_config; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_hotel_config (
    id integer NOT NULL,
    hotel_id integer,
    hotel_name character varying(8000) NOT NULL,
    hotel_luxury_tax_enabled boolean,
    arr_tax_enabled boolean,
    use_arr_for_tax_calculation character varying(255),
    state_id integer,
    declared_tariff_enabled boolean,
    churned boolean,
    hx_id character varying(8000),
    cs_hotel_id character varying(8000),
    state_identifier integer
);


--
-- Name: taxation_hotel_config_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_hotel_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_hotel_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_hotel_config_id_seq OWNED BY taxation_hotel_config.id;


--
-- Name: taxation_hotel_luxurytax; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_hotel_luxurytax (
    id integer NOT NULL,
    hotel_id integer NOT NULL,
    from_price numeric(19,4) NOT NULL,
    to_price numeric(19,4) NOT NULL,
    effective_date date NOT NULL,
    expiry_date date NOT NULL,
    tax numeric(19,4) NOT NULL
);


--
-- Name: taxation_hotel_luxurytax_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_hotel_luxurytax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_hotel_luxurytax_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_hotel_luxurytax_id_seq OWNED BY taxation_hotel_luxurytax.id;


--
-- Name: taxation_other_tax; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_other_tax (
    id integer NOT NULL,
    service_name character varying(8000) NOT NULL,
    service_id integer NOT NULL,
    state_id integer NOT NULL,
    state_identifier integer NOT NULL,
    from_price_pretax numeric(19,4) NOT NULL,
    to_price_pretax numeric(19,4) NOT NULL,
    from_price_posttax numeric(19,4),
    to_price_posttax numeric(19,4),
    effective_date date NOT NULL,
    expiry_date date NOT NULL,
    tax_type_id integer NOT NULL,
    tax_type_name character varying(8000),
    tax_percent numeric(19,4) NOT NULL
);


--
-- Name: taxation_other_tax_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_other_tax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_other_tax_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_other_tax_id_seq OWNED BY taxation_other_tax.id;


--
-- Name: taxation_room_config; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_room_config (
    id integer NOT NULL,
    cs_hotel_id character varying(8000) NOT NULL,
    name character varying(8000) NOT NULL,
    room_code character varying(8000) NOT NULL,
    created_at timestamp(6) without time zone DEFAULT now() NOT NULL,
    modified_at timestamp(6) without time zone DEFAULT now() NOT NULL
);


--
-- Name: taxation_room_config_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_room_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_room_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_room_config_id_seq OWNED BY taxation_room_config.id;



--
-- Name: taxation_service; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_service (
    id integer NOT NULL,
    name character varying(8000) NOT NULL,
    service_accounting_code character varying(8000),
    category_id character varying(8000),
    declared_tariff_required boolean
);


--
-- Name: taxation_service_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_service_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_service_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_service_id_seq OWNED BY taxation_service.id;


--
-- Name: taxation_sgst; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_sgst (
    id integer NOT NULL,
    service_name character varying(8000) NOT NULL,
    state_id integer NOT NULL,
    from_price_pretax numeric(19,4) NOT NULL,
    to_price_pretax numeric(19,4) NOT NULL,
    from_price_posttax numeric(19,4),
    to_price_posttax numeric(19,4),
    effective_date date NOT NULL,
    expiry_date date NOT NULL,
    sgst_percent numeric(19,4) NOT NULL,
    gst_percent numeric(19,4) NOT NULL,
    service_id integer NOT NULL,
    state_identifier integer NOT NULL
);


--
-- Name: taxation_sgst_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_sgst_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_sgst_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_sgst_id_seq OWNED BY taxation_sgst.id;


--
-- Name: taxation_state; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_state (
    id integer NOT NULL,
    state_id integer,
    state_name character varying(8000) NOT NULL,
    cs_state_id character varying(8000)
);


--
-- Name: taxation_state_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_state_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_state_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_state_id_seq OWNED BY taxation_state.id;


--
-- Name: taxation_state_luxurytax; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_state_luxurytax (
    id integer NOT NULL,
    state_id integer NOT NULL,
    from_price numeric(19,4) NOT NULL,
    to_price numeric(19,4) NOT NULL,
    effective_date date NOT NULL,
    expiry_date date NOT NULL,
    tax numeric(19,4) NOT NULL
);


--
-- Name: taxation_state_luxurytax_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_state_luxurytax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_state_luxurytax_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_state_luxurytax_id_seq OWNED BY taxation_state_luxurytax.id;


--
-- Name: taxation_statetax; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_statetax (
    id integer NOT NULL,
    state_id integer NOT NULL,
    tax_type character varying(255) NOT NULL,
    tax numeric(19,4) NOT NULL,
    effective date NOT NULL,
    expiry_date date NOT NULL
);


--
-- Name: taxation_statetax_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_statetax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_statetax_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_statetax_id_seq OWNED BY taxation_statetax.id;

--
-- Name: taxation_tax_type; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE taxation_tax_type (
    id integer NOT NULL,
    name character varying(8000) NOT NULL
);


--
-- Name: taxation_tax_type_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE taxation_tax_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: taxation_tax_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE taxation_tax_type_id_seq OWNED BY taxation_tax_type.id;


--
-- Name: taxation_arr_luxurytax id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_arr_luxurytax ALTER COLUMN id SET DEFAULT nextval('taxation_arr_luxurytax_id_seq'::regclass);


--
-- Name: taxation_cgst id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_cgst ALTER COLUMN id SET DEFAULT nextval('taxation_cgst_id_seq'::regclass);


--
-- Name: taxation_declared_tariff id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_declared_tariff ALTER COLUMN id SET DEFAULT nextval('taxation_declared_tariff_id_seq'::regclass);


--
-- Name: taxation_dt_packet id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_dt_packet ALTER COLUMN id SET DEFAULT nextval('taxation_dt_packet_id_seq'::regclass);


--
-- Name: taxation_global_config id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_global_config ALTER COLUMN id SET DEFAULT nextval('taxation_global_config_id_seq'::regclass);


--
-- Name: taxation_hotel_config id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_config ALTER COLUMN id SET DEFAULT nextval('taxation_hotel_config_id_seq'::regclass);


--
-- Name: taxation_hotel_luxurytax id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_luxurytax ALTER COLUMN id SET DEFAULT nextval('taxation_hotel_luxurytax_id_seq'::regclass);


--
-- Name: taxation_other_tax id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_other_tax ALTER COLUMN id SET DEFAULT nextval('taxation_other_tax_id_seq'::regclass);


--
-- Name: taxation_room_config id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_room_config ALTER COLUMN id SET DEFAULT nextval('taxation_room_config_id_seq'::regclass);


--
-- Name: taxation_service id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_service ALTER COLUMN id SET DEFAULT nextval('taxation_service_id_seq'::regclass);


--
-- Name: taxation_sgst id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_sgst ALTER COLUMN id SET DEFAULT nextval('taxation_sgst_id_seq'::regclass);


--
-- Name: taxation_state id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state ALTER COLUMN id SET DEFAULT nextval('taxation_state_id_seq'::regclass);


--
-- Name: taxation_state_luxurytax id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state_luxurytax ALTER COLUMN id SET DEFAULT nextval('taxation_state_luxurytax_id_seq'::regclass);


--
-- Name: taxation_statetax id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_statetax ALTER COLUMN id SET DEFAULT nextval('taxation_statetax_id_seq'::regclass);


--
-- Name: taxation_tax_type id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_tax_type ALTER COLUMN id SET DEFAULT nextval('taxation_tax_type_id_seq'::regclass);


--
-- Name: taxation_room_config _cs_hotel_id_and_room_code_uc; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_room_config
    ADD CONSTRAINT _cs_hotel_id_and_room_code_uc UNIQUE (cs_hotel_id, room_code);


--
-- Name: taxation_arr_luxurytax taxation_arr_luxurytax_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_arr_luxurytax
    ADD CONSTRAINT taxation_arr_luxurytax_pkey PRIMARY KEY (id);


--
-- Name: taxation_cgst taxation_cgst_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_cgst
    ADD CONSTRAINT taxation_cgst_pkey PRIMARY KEY (id);


--
-- Name: taxation_declared_tariff taxation_declared_tariff_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_declared_tariff
    ADD CONSTRAINT taxation_declared_tariff_pkey PRIMARY KEY (id);


--
-- Name: taxation_dt_packet taxation_dt_packet_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_dt_packet
    ADD CONSTRAINT taxation_dt_packet_pkey PRIMARY KEY (id);


--
-- Name: taxation_global_config taxation_global_config_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_global_config
    ADD CONSTRAINT taxation_global_config_pkey PRIMARY KEY (id);


--
-- Name: taxation_hotel_config taxation_hotel_config_cs_hotel_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_config
    ADD CONSTRAINT taxation_hotel_config_cs_hotel_id_key UNIQUE (cs_hotel_id);


--
-- Name: taxation_hotel_config taxation_hotel_config_hotel_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_config
    ADD CONSTRAINT taxation_hotel_config_hotel_id_key UNIQUE (hotel_id);


--
-- Name: taxation_hotel_config taxation_hotel_config_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_config
    ADD CONSTRAINT taxation_hotel_config_pkey PRIMARY KEY (id);


--
-- Name: taxation_hotel_luxurytax taxation_hotel_luxurytax_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_luxurytax
    ADD CONSTRAINT taxation_hotel_luxurytax_pkey PRIMARY KEY (id);


--
-- Name: taxation_other_tax taxation_other_tax_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_other_tax
    ADD CONSTRAINT taxation_other_tax_pkey PRIMARY KEY (id);


--
-- Name: taxation_room_config taxation_room_config_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_room_config
    ADD CONSTRAINT taxation_room_config_pkey PRIMARY KEY (id);


--
-- Name: taxation_service taxation_service_category_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_service
    ADD CONSTRAINT taxation_service_category_id_key UNIQUE (category_id);


--
-- Name: taxation_service taxation_service_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_service
    ADD CONSTRAINT taxation_service_name_key UNIQUE (name);


--
-- Name: taxation_service taxation_service_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_service
    ADD CONSTRAINT taxation_service_pkey PRIMARY KEY (id);


--
-- Name: taxation_sgst taxation_sgst_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_sgst
    ADD CONSTRAINT taxation_sgst_pkey PRIMARY KEY (id);


--
-- Name: taxation_state taxation_state_cs_state_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state
    ADD CONSTRAINT taxation_state_cs_state_id_key UNIQUE (cs_state_id);


--
-- Name: taxation_state_luxurytax taxation_state_luxurytax_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state_luxurytax
    ADD CONSTRAINT taxation_state_luxurytax_pkey PRIMARY KEY (id);


--
-- Name: taxation_state taxation_state_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state
    ADD CONSTRAINT taxation_state_pkey PRIMARY KEY (id);


--
-- Name: taxation_state taxation_state_state_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state
    ADD CONSTRAINT taxation_state_state_id_key UNIQUE (state_id);


--
-- Name: taxation_statetax taxation_statetax_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_statetax
    ADD CONSTRAINT taxation_statetax_pkey PRIMARY KEY (id);


--
-- Name: taxation_tax_type taxation_tax_type_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_tax_type
    ADD CONSTRAINT taxation_tax_type_name_key UNIQUE (name);


--
-- Name: taxation_tax_type taxation_tax_type_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_tax_type
    ADD CONSTRAINT taxation_tax_type_pkey PRIMARY KEY (id);


--
-- Name: ix_taxation_declared_tariff_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_declared_tariff_created_at ON taxation_declared_tariff USING btree (created_at);


--
-- Name: ix_taxation_declared_tariff_cs_hotel_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_declared_tariff_cs_hotel_id ON taxation_declared_tariff USING btree (cs_hotel_id);


--
-- Name: ix_taxation_declared_tariff_modified_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_declared_tariff_modified_at ON taxation_declared_tariff USING btree (modified_at);


--
-- Name: ix_taxation_global_config_config_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_global_config_config_name ON taxation_global_config USING btree (config_name);


--
-- Name: ix_taxation_global_config_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_global_config_created_at ON taxation_global_config USING btree (created_at);


--
-- Name: ix_taxation_global_config_modified_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_global_config_modified_at ON taxation_global_config USING btree (modified_at);


--
-- Name: ix_taxation_global_config_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_global_config_status ON taxation_global_config USING btree (status);


--
-- Name: ix_taxation_room_config_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_room_config_name ON taxation_room_config USING btree (name);


--
-- Name: ix_taxation_room_config_room_code; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_taxation_room_config_room_code ON taxation_room_config USING btree (room_code);


--
-- Name: taxation_declared_tariff_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX taxation_declared_tariff_index ON taxation_declared_tariff USING btree (hotel_id, room_type_code, from_date, to_date);


--
-- Name: taxation_dt_packet_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX taxation_dt_packet_index ON taxation_dt_packet USING btree (filename, modified_at, status);


--
-- Name: taxation_global_config_index; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX taxation_global_config_index ON taxation_global_config USING btree (config_name, modified_at, created_at, status);


--
-- Name: taxation_arr_luxurytax taxation_arr_luxurytax_hotel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_arr_luxurytax
    ADD CONSTRAINT taxation_arr_luxurytax_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES taxation_hotel_config(hotel_id) ON UPDATE CASCADE;


--
-- Name: taxation_cgst taxation_cgst_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_cgst
    ADD CONSTRAINT taxation_cgst_service_id_fkey FOREIGN KEY (service_id) REFERENCES taxation_service(id) ON UPDATE CASCADE;


--
-- Name: taxation_declared_tariff taxation_declared_tariff_hotel_identifier_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_declared_tariff
    ADD CONSTRAINT taxation_declared_tariff_hotel_identifier_fkey FOREIGN KEY (hotel_identifier) REFERENCES taxation_hotel_config(id) ON UPDATE CASCADE;


--
-- Name: taxation_hotel_config taxation_hotel_config_state_identifier_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_config
    ADD CONSTRAINT taxation_hotel_config_state_identifier_fkey FOREIGN KEY (state_identifier) REFERENCES taxation_state(id) ON UPDATE CASCADE;


--
-- Name: taxation_hotel_luxurytax taxation_hotel_luxurytax_hotel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_hotel_luxurytax
    ADD CONSTRAINT taxation_hotel_luxurytax_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES taxation_hotel_config(hotel_id) ON UPDATE CASCADE;


--
-- Name: taxation_other_tax taxation_other_tax_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_other_tax
    ADD CONSTRAINT taxation_other_tax_service_id_fkey FOREIGN KEY (service_id) REFERENCES taxation_service(id) ON UPDATE CASCADE;


--
-- Name: taxation_other_tax taxation_other_tax_state_identifier_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_other_tax
    ADD CONSTRAINT taxation_other_tax_state_identifier_fkey FOREIGN KEY (state_identifier) REFERENCES taxation_state(id) ON UPDATE CASCADE;


--
-- Name: taxation_other_tax taxation_other_tax_tax_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_other_tax
    ADD CONSTRAINT taxation_other_tax_tax_type_id_fkey FOREIGN KEY (tax_type_id) REFERENCES taxation_tax_type(id) ON UPDATE CASCADE;



--
-- Name: taxation_sgst taxation_sgst_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_sgst
    ADD CONSTRAINT taxation_sgst_service_id_fkey FOREIGN KEY (service_id) REFERENCES taxation_service(id) ON UPDATE CASCADE;


--
-- Name: taxation_sgst taxation_sgst_state_identifier_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_sgst
    ADD CONSTRAINT taxation_sgst_state_identifier_fkey FOREIGN KEY (state_identifier) REFERENCES taxation_state(id) ON UPDATE CASCADE;


--
-- Name: taxation_state_luxurytax taxation_state_luxurytax_state_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_state_luxurytax
    ADD CONSTRAINT taxation_state_luxurytax_state_id_fkey FOREIGN KEY (state_id) REFERENCES taxation_state(state_id) ON UPDATE CASCADE;


--
-- Name: taxation_statetax taxation_statetax_state_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY taxation_statetax
    ADD CONSTRAINT taxation_statetax_state_id_fkey FOREIGN KEY (state_id) REFERENCES taxation_state(state_id) ON UPDATE CASCADE;


--
-- PostgreSQL database dump complete
--

-- downgrade
