-- revision: '20200728115912_global_tax_support'
-- down_revision: '20200721174009_initial_migration'

-- upgrade

CREATE TABLE taxation_tax (
    tax_id integer NOT NULL,
    tax_code character varying,
    description character varying,
    unit character varying,
    name character varying
);

CREATE TABLE taxation_tax_config (
    tax_config_id integer NOT NULL,
    service_category_id character varying,
    tax_type character varying NOT NULL,
    date_range daterange NOT NULL,
    pretax_range numrange NOT NULL,
    tax_value numeric NOT NULL,
    included_in_rate boolean,
    weekdays_byte integer NOT NULL,
    available_for_all_hotels boolean NOT NULL DEFAULT TRUE,
    tax_id integer NOT NULL
);

CREATE TABLE taxation_seller (
    seller_id character varying NOT NULL,
    name character varying,
    category character varying,
    state_id integer
);

CREATE TABLE taxation_hotel_tax_config_mapping (
    hotel_id character varying NOT NULL,
    tax_config_id integer NOT NULL
);

CREATE TABLE taxation_seller_tax_config_mapping (
    seller_id character varying NOT NULL,
    tax_config_id integer NOT NULL
);

CREATE OR REPLACE FUNCTION trg_t_name_conflicting_tax_config_prohibited()
  R<PERSON><PERSON>NS trigger AS
$func$
DECLARE
    tax_config_id    INTEGER;
BEGIN
    SET search_path FROM CURRENT;
    IF EXISTS (
        SELECT 1
        FROM taxation_tax_config
        WHERE taxation_tax_config.tax_config_id != NEW.tax_config_id
        AND tax_id = NEW.tax_id
        AND service_category_id = NEW.service_category_id
        AND date_range && NEW.date_range
        AND pretax_range && NEW.pretax_range
        AND weekdays_byte & NEW.weekdays_byte != 0
        AND available_for_all_hotels = NEW.available_for_all_hotels
    ) THEN
        SELECT taxation_tax_config.tax_config_id INTO tax_config_id
        FROM taxation_tax_config
        WHERE taxation_tax_config.tax_config_id != NEW.tax_config_id
        AND tax_id = NEW.tax_id
        AND service_category_id = NEW.service_category_id
        AND date_range && NEW.date_range
        AND pretax_range && NEW.pretax_range
        AND weekdays_byte & NEW.weekdays_byte != 0
        AND available_for_all_hotels = NEW.available_for_all_hotels;
        RAISE 'Conflicting tax_config record found with Tax Config Id: %', tax_config_id USING ERRCODE = 'unique_violation';
END IF;
RETURN NEW;
END
$func$ LANGUAGE plpgsql;


SET default_tablespace = '';

CREATE SEQUENCE taxation_tax_config_tax_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE taxation_tax_config_tax_config_id_seq OWNED BY taxation_tax_config.tax_config_id;

CREATE SEQUENCE taxation_tax_tax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE taxation_tax_tax_id_seq OWNED BY taxation_tax.tax_id;

ALTER TABLE ONLY taxation_tax ALTER COLUMN tax_id SET DEFAULT nextval('taxation_tax_tax_id_seq'::regclass);

ALTER TABLE ONLY taxation_tax_config ALTER COLUMN tax_config_id SET DEFAULT nextval('taxation_tax_config_tax_config_id_seq'::regclass);

ALTER TABLE ONLY taxation_tax_config
    ADD CONSTRAINT taxation_tax_config_pkey PRIMARY KEY (tax_config_id);

ALTER TABLE ONLY taxation_tax
    ADD CONSTRAINT taxation_tax_pkey PRIMARY KEY (tax_id);

ALTER TABLE ONLY taxation_seller
    ADD CONSTRAINT taxation_seller_pkey PRIMARY KEY (seller_id);

ALTER TABLE ONLY taxation_hotel_tax_config_mapping
    ADD CONSTRAINT taxation_hotel_tax_config_mapping_pkey PRIMARY KEY (hotel_id, tax_config_id);

ALTER TABLE ONLY taxation_seller_tax_config_mapping
    ADD CONSTRAINT taxation_seller_tax_config_mapping_pkey PRIMARY KEY (seller_id, tax_config_id);

ALTER TABLE ONLY taxation_tax
    ADD CONSTRAINT taxation_tax_tax_code_key UNIQUE (tax_code);

ALTER TABLE ONLY taxation_tax_config
    ADD CONSTRAINT unique_tax_config_for_tax_id_service_date_price_range EXCLUDE USING gist (tax_id WITH =, service_category_id WITH =, date_range WITH &&, pretax_range WITH &&);

CREATE INDEX ix_tax_config_tax_id_service_category ON taxation_tax_config USING btree (tax_id, service_category_id);

CREATE TRIGGER check_tax_config_conflict_before_insert_update
BEFORE INSERT OR UPDATE OF tax_id, service_category_id, date_range, pretax_range, weekdays_byte, available_for_all_hotels
ON taxation_tax_config
FOR EACH ROW
EXECUTE PROCEDURE trg_t_name_conflicting_tax_config_prohibited();

ALTER TABLE ONLY taxation_tax_config
    ADD CONSTRAINT taxation_tax_config_service_category_id_fkey FOREIGN KEY (service_category_id) REFERENCES taxation_service(category_id);

ALTER TABLE ONLY taxation_tax_config
    ADD CONSTRAINT taxation_tax_config_tax_id_fkey FOREIGN KEY (tax_id) REFERENCES taxation_tax(tax_id);

ALTER TABLE ONLY taxation_hotel_tax_config_mapping
    ADD CONSTRAINT taxation_hotel_tax_config_mapping_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES taxation_hotel_config(cs_hotel_id);

ALTER TABLE ONLY taxation_hotel_tax_config_mapping
    ADD CONSTRAINT taxation_hotel_tax_config_mapping_tax_config_id_fkey FOREIGN KEY (tax_config_id) REFERENCES taxation_tax_config(tax_config_id);

ALTER TABLE ONLY taxation_seller_tax_config_mapping
    ADD CONSTRAINT taxation_seller_tax_config_mapping_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES taxation_seller(seller_id);

ALTER TABLE ONLY taxation_seller_tax_config_mapping
    ADD CONSTRAINT taxation_seller_tax_config_mapping_tax_config_id_fkey FOREIGN KEY (tax_config_id) REFERENCES taxation_tax_config(tax_config_id);

-- downgrade

DROP TRIGGER check_tax_config_conflict_before_insert_update ON taxation_tax_config;

DROP TABLE taxation_hotel_tax_config_mapping;

DROP TABLE taxation_seller_tax_config_mapping;

DROP TABLE taxation_seller;

DROP TABLE taxation_tax_config;

DROP TABLE taxation_tax;




