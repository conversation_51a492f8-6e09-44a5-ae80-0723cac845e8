-- revision: '20210921193759_taxable_amount_rules'
-- down_revision: '20210907171919_remove_service_unique_checks'

-- upgrade
CREATE INDEX idx_taxation_tax_config_service_category_id ON taxation_tax_config (service_category_id);
CREATE TABLE taxation_taxable_amount_rule (
    rule_id integer NOT NULL,
    service_category_id character varying,
    percentage_of_total_value numeric NOT NULL,
    minimum_taxable_amount numeric NOT NULL
);

CREATE TABLE taxation_hotel_taxable_amount_rule_mapping (
    hotel_id character varying NOT NULL,
    taxable_amount_rule_id integer NOT NULL
);

CREATE SEQUENCE taxation_taxable_amount_rule_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE taxation_taxable_amount_rule_id_seq OWNED BY taxation_taxable_amount_rule.rule_id;

ALTER TABLE ONLY taxation_taxable_amount_rule ALTER COLUMN rule_id SET DEFAULT nextval('taxation_taxable_amount_rule_id_seq'::regclass);

ALTER TABLE ONLY taxation_taxable_amount_rule
    ADD CONSTRAINT taxation_taxable_amount_rule_pkey PRIMARY KEY (rule_id);


ALTER TABLE ONLY taxation_hotel_taxable_amount_rule_mapping
    ADD CONSTRAINT taxation_hotel_taxable_amount_rule_mapping_pkey PRIMARY KEY (hotel_id, taxable_amount_rule_id);

ALTER TABLE ONLY taxation_hotel_taxable_amount_rule_mapping
    ADD CONSTRAINT taxation_hotel_taxable_amount_rule_mapping_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES taxation_hotel_config(cs_hotel_id);

ALTER TABLE ONLY taxation_hotel_taxable_amount_rule_mapping
    ADD CONSTRAINT taxation_hotel_taxable_amount_rule_mapping_rule_fkey FOREIGN KEY (taxable_amount_rule_id) REFERENCES taxation_taxable_amount_rule(rule_id);

-- downgrade
DROP TABLE taxation_hotel_taxable_amount_rule_mapping;
DROP TABLE taxation_taxable_amount_rule;
DROP INDEX idx_taxation_tax_config_service_category_id;
