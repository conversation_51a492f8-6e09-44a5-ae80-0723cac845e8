from decimal import Decimal, ROUND_HALF_UP

TWO_DECIMAL = Decimal('.01')
ZERO_DECIMAL = Decimal('1.')

ZERO = Decimal('0.0')


class NumberUtils:
    def __init__(self):
        pass

    @staticmethod
    def round_to_two_decimal(price):
        """
        decimal point accuracy till 4th decimal place

        sample: 1.056 -> 1.06, 1.055 -> 1.06, 1.054 -> 1.05
        :param price:
        :return: rounded off price value
        """
        return Decimal(price).quantize(TWO_DECIMAL, rounding=ROUND_HALF_UP)

    @staticmethod
    def round_to_nearest_integer(price):
        return Decimal(price).quantize(ZERO_DECIMAL, rounding=ROUND_HALF_UP)
