import logging

import requests
from flask import current_app

logger = logging.getLogger(__name__)

dt_related_error = ['dt', 'dt_error']
channel_list = ['dt', 'dt_error', 'consumer', 'configs']


def slack_alert(data=None, message=None, channel=None):
    slack_text = create_slack_message(data, message)
    url = get_url(channel)

    # mrkdwn= {True:formatting enabled, False: formatting disabled}
    json_data = {
        "text": slack_text,
        "username": "taxation_bot",
        "mrkdwn": True
    }
    try:
        requests.post(url, json=json_data)
    except:
        logger.exception("Failed to fire slack alert. Data: %s" % message)


def create_slack_message(data=None, message=None):
    environment = current_app.config.get('ENVIRONMENT')
    text = ""
    if message:
        text = "Service: `Taxation`, Env: `%s`. Message: ```%s```\n" % (environment, message)
    if data:
        text = "%sData: ```%s```" % (text, str(data))
    return text


def get_url(channel):
    if channel:
        if channel in dt_related_error:
            return current_app.config.get('SLACKBOT_HOOK')
    return current_app.config.get('CONFIGS_SLACKBOT_HOOK')
