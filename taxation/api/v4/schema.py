from decimal import Decimal

from marshmallow import (Schema, fields, validates_schema, validate, ValidationError)
from taxation.api import validators
from taxation.api.schema import ProductAttributes

__all__ = ['TaxResponseSchemaV4', 'ProductBookingV4', 'ProductSchemaV4', 'TaxRequestSchemaV4', 'ResponseTaxDetailsV4',
           'ResponseTaxBreakupComponentV4', 'ResponseProductBookingV4', 'ResponseProductSchemaV4', 'SchemaV4',
           'SkuPriceSchema', 'ResponseSkuTaxDetailsV4']


class SkuPriceSchema(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    sku_code = fields.String(required=True, validate=validate.Length(min=1))
    sku_price = fields.Decimal(required=True, allow_none=False, validate=validate.Range(min=Decimal('0.0'),
                                                                                        error='Price should not be '
                                                                                              'negative'))


class ProductBookingV4(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    sku_prices = fields.Nested(SkuPriceSchema, many=True, required=True)
    date = fields.Date(required=True, allow_none=False)


class SchemaV4(Schema):
    @validates_schema
    def validate_object(self, data):
        attributes = data['attributes']
        validator = validators.ProductAttributeValidator()
        if validator:
            validator.validate_product_attributes(attributes)

        hotel_id_present = False
        for attribute in attributes:
            if attribute.get('key', '').lower() == 'hotel_id':
                hotel_id_present = True
        if not hotel_id_present:
            raise ValidationError('hotel_id attribute missing')


class ProductSchemaV4(SchemaV4):
    index = fields.String(required=True, validate=validate.Length(min=1))
    is_sez = fields.Boolean(missing=False)
    gstin = fields.String(missing='')
    prices = fields.Nested(ProductBookingV4, many=True, required=True)
    attributes = fields.Nested(ProductAttributes, many=True, required=True)
    buyer_has_lut = fields.Boolean(missing=False)
    seller_has_lut = fields.Boolean(missing=False)

    @validates_schema
    def validate_object(self, data):
        super(ProductSchemaV4, self).validate_object(data)


class TaxRequestSchemaV4(Schema):
    skus = fields.Nested(ProductSchemaV4, many=True, required=True)


class ResponseTaxBreakupComponentV4(Schema):
    percent = fields.Decimal(required=True, allow_none=False,
                             validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    value = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    type = fields.String(required=True, validate=validate.Length(min=1))


class ResponseTaxDetailsV4(Schema):
    percent = fields.Decimal(required=True, allow_none=False,
                             validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    value = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    breakup = fields.Nested(ResponseTaxBreakupComponentV4, many=True, required=True)


class ResponseSkuTaxDetailsV4(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    tax = fields.Nested(ResponseTaxDetailsV4, many=True, required=True)


class ResponseProductBookingV4(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    pre_tax_price = fields.Decimal(required=True, allow_none=False,
                                   validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    post_tax_price = fields.Decimal(required=True, allow_none=False,
                                    validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    date = fields.Date(required=True, allow_none=False)
    tax = fields.Nested(ResponseSkuTaxDetailsV4, many=True, required=True)


class ResponseProductSchemaV4(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    prices = fields.Nested(ResponseProductBookingV4, many=True, required=True)


class TaxResponseSchemaV4(Schema):
    skus = fields.Nested(ResponseProductSchemaV4, many=True, required=True)
    # data = fields.Object(object, many=True, required=True)
