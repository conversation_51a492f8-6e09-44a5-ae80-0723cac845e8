import logging

from flask import Blueprint

from taxation.api.request_parsers import raw_json
from taxation.api.v4 import schema
from taxation.decorators import json_response
from taxation.domain.tax import repo_provider
from taxation.domain.tax.services.tax_v4_api_service import TaxV4ApiService

bp = Blueprint("tax_v4", __name__)

logger = logging.getLogger(__name__)
request_logger = logging.getLogger('request_handler')

'''

TO DO
------

1)Add miscellaneous in CGST and SGST tables(Prod) with correct per state sgst,cgst values


{
    "skus": [{
        "index": "1",
        "is_sez": false,
        "attributes": [{
            "key": "hotel_id",
            "value": "0016932"
        }],
        "prices": [{
            "index": "1",
            "date": "2018-07-29",
            "sku_prices": [{
                "index" : "1",
                "sku_code": "2cca7c8d70e94df59249f508cf94d5f0",
                "sku_price": 2250.0
            }]
        }]
    }]
}

'''


@bp.route("/calculate_tax", methods=['POST'])
@raw_json(schema.TaxRequestSchemaV4)
@json_response()
def calculate_tax_api(parsed_request):
    # TODO: Used in PO and Direct
    """
    Given endpoint(/tax/v4/calculate_tax) does tax(post-tax) calculation.

    ---
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: does tax(post-tax) calculation.
        tags:
            - Tax V4 (Sku Code Based)
        parameters:
            - in: body
              name: body
              description: does tax(post-tax) calculation.
              required: True
              schema: TaxRequestSchemaV4
        responses:
            200:
                description: Tax(Post-tax) computation response
                schema: TaxResponseSchemaV4
    """
    response_data = TaxV4ApiService(repo_provider).calculate_tax(parsed_request)
    return response_data, 200
