import logging
import functools
from flask import request
from taxation import messages
from taxation.exceptions import InvalidRequestData

logger = logging.getLogger(__name__)


def validator(schema, request_attr):
    """
        @request_attr is 'args' for GET APIs
                         'form' for POST APIs
                         'json' for POST APIs content-type application/json
    """
    def validate_decorator(f):
        def wrapper(*args, **kwargs):
            try:
                request_data = getattr(request, request_attr)
                parsed_data = schema().load(request_data)
                if parsed_data.errors:
                    logger.error("Errors from %s schema: %s", schema, parsed_data.errors)
                    raise InvalidRequestData(messages.API_REQUEST_DATA_VALIDATION_FAILED,
                                             context=parsed_data.errors)
                kwargs['parsed_request'] = parsed_data.data
            except InvalidRequestData as e:
                raise e
            return f(*args, **kwargs)
        return functools.update_wrapper(wrapper, f)
    return validate_decorator


def query_params(schema):
    return validator(schema, 'args')


def form_data(schema):
    return validator(schema, 'form')


def raw_json(schema):
    return validator(schema, 'json')
