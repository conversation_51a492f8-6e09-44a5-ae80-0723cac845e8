"""
:mod:`.cache_api` -- Tax Cache API
===========================================

.. module:: cache_api

"""
import logging

from flask import Blueprint

from taxation.infrastructure import cache
from taxation.decorators import json_response

bp = Blueprint("tax_cache", __name__)

logger = logging.getLogger(__name__)
request_logger = logging.getLogger('request_handler')


@bp.route("/refresh_cache", methods=['GET'])
@json_response()
def refresh_cache():
    logger.debug("Refreshing Tax Redis Cache")
    cache.refresh_redis_cache()
    return {"message": "Cache Refreshed"}, 200
