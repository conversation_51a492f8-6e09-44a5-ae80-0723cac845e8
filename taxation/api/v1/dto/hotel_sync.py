
class HotelSync(object):
    """
    Domain object to represent a tax object instance
    :param hotel_id
    :param hotel_name
    """
    def __init__(self, hotel_id, hotel_name, state_name, state_id, rooms=None, hx_id=None, catalog_property_id=None):
        self.hotel_id = hotel_id
        self.hx_id = hx_id
        self.hotel_name = hotel_name
        self.state_name = state_name
        self.state_id = state_id
        self.catalog_property_id = catalog_property_id

    def __str__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__
