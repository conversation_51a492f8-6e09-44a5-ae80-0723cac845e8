from decimal import Decimal

from marshmallow import (Schema, fields, validates_schema, validate, ValidationError)

from taxation.api import validators


class SkuAttributeSchema(Schema):
    key = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    value = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class SkuPriceSchema(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    price = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    date = fields.Date(required=True, allow_none=False)


class SchemaV3(Schema):
    @validates_schema
    def validate_object(self, data):
        if not (data.get("sku_code") or data.get("category_id")):
            raise ValidationError("Please provider either sku_code or category_id")
        attributes = data['attributes']
        validator = validators.ProductAttributeValidator()
        if validator:
            validator.validate_product_attributes(attributes)

        hotel_id_present = False
        for attribute in attributes:
            if attribute.get('key', '').lower() == 'hotel_id':
                hotel_id_present = True
        if not hotel_id_present:
            raise ValidationError('hotel_id attribute missing')


class SkuSchema(SchemaV3):
    index = fields.String(required=True, validate=validate.Length(min=1))
    sku_code = fields.String(required=False, validate=validate.Length(min=1),
                             description="Either category_id or this field is required")
    category_id = fields.String(required=False, validate=validate.Length(min=1),
                                description="Either sku_code or this field is required")
    prices = fields.Nested(SkuPriceSchema, many=True, required=True)
    attributes = fields.Nested(SkuAttributeSchema, many=True, required=True)

    @validates_schema
    def validate_object(self, data):
        prices = data.get("prices")
        price_index = [price.get("index") for price in prices]
        if len(set(price_index)) != len(price_index):
            raise ValidationError(
                "Duplicate SKU Price Index found. Please ensure to pass unique index per price under an sku, "
                "to uniquely identify the map the tax response its price", ["prices"])
        super(SkuSchema, self).validate_object(data)


class BookingSchema(Schema):
    is_sez = fields.Boolean(missing=False)
    gstin = fields.String(missing='')
    buyer_has_lut = fields.Boolean(missing=False)
    seller_has_lut = fields.Boolean(missing=False)
    skus = fields.Nested(SkuSchema, many=True, required=True)

    @validates_schema
    def validate_object(self, data):
        skus = data.get("skus")
        sku_index = [sku.get("index") for sku in skus]
        if len(set(sku_index)) != len(sku_index):
            raise ValidationError(
                "Duplicate SKU Index found. Please ensure to pass unique index per sku, to uniquely identify the map "
                "the tax response with skus", ["skus"])


class ResponseTaxBreakupComponent(Schema):
    percent = fields.Decimal(required=True, allow_none=False,
                             validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    value = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    type = fields.String(required=True, validate=validate.Length(min=1))


class TaxDetailsSchema(Schema):
    percent = fields.Decimal(required=True, allow_none=False,
                             validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    value = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    breakup = fields.Nested(ResponseTaxBreakupComponent, many=True, required=True)


class SkuPriceResponseSchema(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    tax = fields.Nested(TaxDetailsSchema, required=True)
    date = fields.Date(required=True, allow_none=False)
    pretax_price = fields.Decimal(required=True, allow_none=False,
                                  validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    posttax_price = fields.Decimal(required=True, allow_none=False,
                                   validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))


class SkuResponseSchema(SchemaV3):
    index = fields.String(required=True, validate=validate.Length(min=1))
    prices = fields.Nested(SkuPriceResponseSchema, many=True, required=True)


class TaxResponseSchema(Schema):
    is_sez = fields.Boolean()
    gstin = fields.String()
    buyer_has_lut = fields.Boolean()
    seller_has_lut = fields.Boolean()
    skus = fields.Nested(SkuResponseSchema, many=True, required=True)
