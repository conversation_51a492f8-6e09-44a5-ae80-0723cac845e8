"""
:mod:`.tax_api` -- Tax API
===========================================

.. module:: tax_api

"""
import logging

from flask import Blueprint, request

from taxation.alerts import slack_alert
from taxation.api.request_parsers import raw_json
from taxation.api.v1 import schema
from taxation.api.v1.dto.hotel_sync import HotelSync
from taxation.api.v1.schema import TaxResponseSchema
from taxation.api.v1.tax_request_builder import TaxRequestBuilder
from taxation.decorators import json_response
from taxation.domain.tax.schema import PropertySyncWebSchema
from taxation.domain.tax.services import ConfigManagementService
from taxation.domain.tax.services.tax_service import TaxService

bp = Blueprint("tax_v1", __name__)

logger = logging.getLogger(__name__)
request_logger = logging.getLogger('request_handler')


@bp.route("/calculate_tax", methods=['POST'])
@raw_json(schema.BookingSchema)
@json_response()
def calculate_tax_api(parsed_request):
    """Replaces V3 and V4 Tax APIs
        This Tax API computes tax, taking skus in both older format (via, category_id) and newer format
        (via, sku_code). With this API, we can get rid of 2 versions of API currently used (v3 and v4)
    ---
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: true
    post:
        description: This Tax API computes tax, taking skus in both older format (via, category_id) and newer format
            (via, sku_code). With this API, we can get rid of 2 versions of API currently used (v3 and v4)
        tags:
            - Tax V1 (SKU or Category)
        parameters:
            - in: body
              name: body
              description:
              required: True
              schema: BookingSchema
        responses:
            200:
                description: Tax computation response
                schema: TaxResponseSchema
    """
    booking = TaxRequestBuilder().build(parsed_request.get("skus"), parsed_request.get("is_sez"),
                                        parsed_request.get("buyer_has_lut"), parsed_request.get("seller_has_lut"),
                                        parsed_request.get("gstin"))
    TaxService().compute_tax(booking)
    return TaxResponseSchema().dump(booking).data, 200


@bp.route("/calculate_pretax", methods=['POST'])
@raw_json(schema.BookingSchema)
@json_response()
def calculate_pretax_api(parsed_request):
    """Replaces V3 and V4 Tax APIs
        This Tax API computes pretax prices, taking skus in both older format (via, category_id) and newer format
        (via, sku_code). With this API, we can get rid of 2 versions of API currently used (v3 and v4)
    ---
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: true
    post:
        description: This Tax API computes tax, taking skus in both older format (via, category_id) and newer format
            (via, sku_code). With this API, we can get rid of 2 versions of API currently used (v3 and v4)
        tags:
            - Tax V1 (SKU or Category)
        parameters:
            - in: body
              name: body
              description:
              required: True
              schema: BookingSchema
        responses:
            200:
                description: Tax computation response
                schema: TaxResponseSchema
    """
    booking = TaxRequestBuilder().build(parsed_request.get("skus"), parsed_request.get("is_sez"),
                                        parsed_request.get("buyer_has_lut"), parsed_request.get("seller_has_lut"),
                                        parsed_request.get("gstin"))
    TaxService().compute_tax(booking, reverse_tax=True)
    return TaxResponseSchema().dump(booking).data, 200


@bp.route("/hotel_sync", methods=['POST'])
@json_response()
def property_sync_from_web():
    """
    This end-point stores the hotel at tax service when the there is a sync from website to pricing-service
    request: {
        "hotel_id": "16216",
        "hotel_name": "treebo silicon business",
        "state_id": 1,
        "hx_id" = "1234",
        "state_name": "Karnataka"
    }
    :return:
    response: {"message": "%s Added Successfully" % hotel_name}, 200
    """
    data = request.json
    if not data:
        return {"message": "Empty data sent"}, 400

    parsed_data, error = PropertySyncWebSchema().load(data)
    if error:
        logger.error("in store_hotel_on_sync data format error,Data:%s, Error:%s" % (data, error))
        slack_alert(data=data, message="store_hotel_on_sync data format error, error: %s" % str(error))
        return {"message": "Incoming data format error, Data: %s" % str(data)}, 400
    hotel_id = parsed_data.get("hotel_id")
    hx_id = parsed_data.get("hx_id")
    hotel_name = parsed_data.get("hotel_name")
    state_id = parsed_data.get('state_id')
    state_name = parsed_data.get('state_name', "")
    hotel_dto = HotelSync(hotel_id, hotel_name, state_name, state_id, hx_id=hx_id,
                          catalog_property_id=parsed_data.get('catalog_property_id'))
    hotel_object, status = ConfigManagementService.hotel_sync_from_web(hotel_dto)

    if hotel_object:
        return {"message": "%s(Id: %s) Updated Successfully" % (hotel_name, hotel_id)}, 200
    if status:
        return {"message": "Request received but hotel creation not supported"}, 200
    return {"message": "Error happened, kindly check logs and slack alerts. Data: %s" % hotel_dto.__dict__}, 400
