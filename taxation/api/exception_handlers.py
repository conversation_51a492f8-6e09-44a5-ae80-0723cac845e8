import logging
import sys
import traceback

from flask import request

from taxation.alerts import slack_alert
from taxation.api.v1.tax_api import bp as tax_bp
from taxation.api.v3.tax_api import bp as tax_v3_bp
from taxation.api.v4.tax_api import bp as tax_v4_bp
from taxation.api.v5.tax_api import bp as tax_v5_bp
from taxation.api.v6.tax_api import bp as tax_v6_bp
from taxation.decorators import json_response
from taxation.exceptions import (
    ResourceNotFound, InvalidRequestData, TaxConfigurationError, InvalidProductType
)

logger = logging.getLogger('error.' + __name__)


@tax_bp.errorhandler(InvalidRequestData)
@tax_v3_bp.errorhandler(InvalidRequestData)
@tax_v4_bp.errorhandler(InvalidRequestData)
@tax_v5_bp.errorhandler(InvalidRequestData)
@tax_v6_bp.errorhandler(InvalidRequestData)
@json_response()
def invalid_request_data_handler(error):
    logger.exception(error)
    return error.__dict__, 400


@tax_bp.errorhandler(TaxConfigurationError)
@tax_v3_bp.errorhandler(TaxConfigurationError)
@tax_v4_bp.errorhandler(TaxConfigurationError)
@json_response()
def tax_config_error_handler(error):
    logger.exception(error)
    return error.__dict__, 400


@tax_bp.errorhandler(ResourceNotFound)
@tax_v3_bp.errorhandler(ResourceNotFound)
@tax_v4_bp.errorhandler(ResourceNotFound)
@json_response()
def resource_not_found_handler(error):
    logger.exception(error)
    return error.__dict__, 404


@tax_bp.errorhandler(InvalidProductType)
@tax_v3_bp.errorhandler(InvalidProductType)
@tax_v4_bp.errorhandler(InvalidProductType)
@json_response()
def invalid_product_type_handler(error):
    logger.exception(error)
    return error.__dict__, 400


@tax_bp.errorhandler(Exception)
@tax_v3_bp.errorhandler(Exception)
@tax_v4_bp.errorhandler(Exception)
@tax_v5_bp.errorhandler(Exception)
@tax_v6_bp.errorhandler(Exception)
@json_response()
def all_exception_handler(error):
    generic_exception_handler(error)
    return dict(message='Unknown Exception occurred'), 500


def generic_exception_handler(error):
    exc_type, exc_value, exc_traceback = sys.exc_info()
    logger.exception(error, exc_info=True)
    tb = repr(traceback.format_exception(exc_type, exc_value, exc_traceback))
    if error.__dict__:
        slack_alert(message="Url: %s, Error: %s\nTraceback: %s" % (request.url, repr(error), tb), data=error.__dict__)
    else:
        slack_alert(message="Url: %s, Error: %s\nTraceback: %s" % (request.url, repr(error), tb))
