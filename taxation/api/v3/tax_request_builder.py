from taxation.domain.tax.dto.booking_dto import BookingDto, SkuPrice, Sku


class TaxRequestBuilderV3(object):
    @classmethod
    def build(cls, skus):
        is_sez, gstin, buyer_has_lut, seller_has_lut = False, "", False, False
        parsed_skus = []
        for sku in skus:
            is_sez, gstin, buyer_has_lut, seller_has_lut = sku.get("is_sez"), sku.get("gstin"), sku.get(
                "buyer_has_lut"), sku.get("seller_has_lut")
            index = sku["index"]
            prices = sku["prices"]
            attributes = sku["attributes"]
            category_id = sku.get("category_id")

            parsed_skus.append(cls._build_sku(index, None, category_id, prices, attributes))

        booking = BookingDto(skus=parsed_skus, is_sez=is_sez, buyer_has_lut=buyer_has_lut,
                             seller_has_lut=seller_has_lut, gstin=gstin)
        return booking

    @classmethod
    def _build_sku(cls, sku_index, sku_code, category_id, prices, attributes):
        sku_attributes = {d['key']: d['value'] for d in attributes}
        sku_prices = []
        for p in prices:
            date = p["date"]
            price_index = p["index"]
            price = p["price"]
            sku_prices.append(SkuPrice(date, price_index, price))

        sku = Sku(sku_index, sku_prices, sku_attributes, sku_code=sku_code, sku_category_code=category_id)
        return sku
