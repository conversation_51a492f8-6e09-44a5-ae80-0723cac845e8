import logging

from flask import Blueprint

from taxation.api.request_parsers import raw_json
from taxation.api.v3 import schema
from taxation.api.v3.schema import TaxResponseSchemaV3
from taxation.api.v3.tax_request_builder import TaxRequestBuilderV3
from taxation.decorators import json_response
from taxation.domain.tax.services.tax_service import TaxService

bp = Blueprint("tax_v3", __name__)

logger = logging.getLogger(__name__)
request_logger = logging.getLogger('request_handler')


@bp.route("/calculate_tax", methods=['POST'])
@raw_json(schema.TaxRequestSchemaV3)
@json_response()
def calculate_tax_api(parsed_request):
    # TODO: Used in CRS, B2B, Reseller, PO and Direct
    """Given endpoint(/tax/v3/calculate_tax) does tax(post-tax) calculation.
    This is using docstrings for specifications.
    ---
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: does tax(post-tax) calculation.
        tags:
            - Tax V3 (Category Based)
        parameters:
            - in: body
              name: body
              description: does tax(post-tax) calculation.
              required: True
              schema: TaxRequestSchemaV3
        responses:
            200:
                description: Tax(Post-tax) computation response
                schema: TaxResponseSchemaV3
    """
    logger.info("calculate_tax API Request: %s", parsed_request)
    return tax_compute(parsed_request, forward_tax_compute=True)


@bp.route("/calculate_pretax", methods=['POST'])
@raw_json(schema.TaxRequestSchemaV3)
@json_response()
def reverse_tax_api(parsed_request):
    """Given endpoint(/tax/v3/calculate_pretax) does reverse-tax(pre-tax) calculation.
        This is using docstrings for specifications.
        ---
        consumes:
            - application/json
        produces:
            - application/json
        schemes: ['http', 'https']
        deprecated: true
        post:
            description: does reverse-tax(pre-tax) calculation.
            tags:
                - Tax V3 (Category Based)
            parameters:
                - in: body
                  name: body
                  description: does reverse-tax(pre-tax) calculation.
                  required: True
                  schema: TaxRequestSchemaV3
            responses:
                200:
                    description: Reverse-Tax(Pre-tax) computation response
                    schema: TaxResponseSchemaV3
        """
    logger.info("calculate_pretax API Request: %s", parsed_request)
    return tax_compute(parsed_request, forward_tax_compute=False)


def tax_compute(parsed_request, forward_tax_compute=True):
    booking = TaxRequestBuilderV3().build(parsed_request.get("skus"))
    TaxService().compute_tax(booking, reverse_tax=not forward_tax_compute)
    return TaxResponseSchemaV3().dump(booking).data, 200
