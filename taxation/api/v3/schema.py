from decimal import Decimal

from marshmallow import (Schema, fields, validates_schema, validate, ValidationError)

from taxation.api import validators
from taxation.api.schema import ProductAttributes


class ProductBookingV3(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    price = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    date = fields.Date(required=True, allow_none=False)


class SchemaV3(Schema):
    @validates_schema
    def validate_object(self, data):
        attributes = data['attributes']
        validator = validators.ProductAttributeValidator()
        if validator:
            validator.validate_product_attributes(attributes)

        hotel_id_present = False
        state_id_present = False
        for attribute in attributes:
            if attribute.get('key', '').lower() == 'hotel_id':
                hotel_id_present = True
            if attribute.get('key', '').lower() == 'state_id':
                state_id_present = True
        if not hotel_id_present and not state_id_present:
            raise ValidationError('hotel_id or state_id attribute missing')


class ProductSchemaV3(SchemaV3):
    index = fields.String(required=True, validate=validate.Length(min=1))
    category_id = fields.String(required=True, validate=validate.Length(min=1))
    prices = fields.Nested(ProductBookingV3, many=True, required=True)
    attributes = fields.Nested(ProductAttributes, many=True, required=True)
    is_sez = fields.Boolean(missing=False)
    gstin = fields.String(missing='')
    buyer_has_lut = fields.Boolean(missing=False)
    seller_has_lut = fields.Boolean(missing=False)

    @validates_schema
    def validate_object(self, data):
        super(ProductSchemaV3, self).validate_object(data)


class TaxRequestSchemaV3(Schema):
    skus = fields.Nested(ProductSchemaV3, many=True, required=True)


class ResponseTaxBreakupComponentV3(Schema):
    percent = fields.Decimal(required=True, allow_none=False,
                             validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    value = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    type = fields.String(required=True, validate=validate.Length(min=1))


class TaxDetailsSchemaV3(Schema):
    percent = fields.Decimal(required=True, allow_none=False,
                             validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    value = fields.Decimal(required=True, allow_none=False,
                           validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    breakup = fields.Nested(ResponseTaxBreakupComponentV3, many=True, required=True)


class SkuPriceResponseSchemaV3(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    pre_tax_price = fields.Decimal(required=True, allow_none=False,
                                   validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    post_tax_price = fields.Decimal(required=True, allow_none=False,
                                    validate=validate.Range(min=Decimal('0.0'), error='Price should not be negative'))
    date = fields.Date(required=True, allow_none=False)
    tax = fields.Nested(TaxDetailsSchemaV3, required=True)


class SkuResponseSchemaV3(Schema):
    index = fields.String(required=True, validate=validate.Length(min=1))
    prices = fields.Nested(SkuPriceResponseSchemaV3, many=True, required=True)


class TaxResponseSchemaV3(Schema):
    skus = fields.Nested(SkuResponseSchemaV3, many=True, required=True)
