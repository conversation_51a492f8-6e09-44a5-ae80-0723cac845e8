import abc
import logging
from collections import Counter

from marshmallow import ValidationError

logger = logging.getLogger(__name__)


class ProductValidator(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def validate_product_attributes(self, product_attributes):
        return


class ProductAttributeValidator(object):
    @staticmethod
    def validate_product_attributes(product_attributes):
        keys = [attribute.get('key') for attribute in product_attributes]
        duplicate_keys = [k for k, v in Counter(keys).items() if v != 1]
        if duplicate_keys:
            raise ValidationError('Duplicate attributes received: %s' % set(duplicate_keys), ['attributes'])
