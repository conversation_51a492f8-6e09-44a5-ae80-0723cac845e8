from taxation.domain.tax.dto.sku_attributes_dto import SkuAttributeDTO, AttributeKey
from taxation.domain.tax.dto.sku_price_dto import SkuPriceDTO
from taxation.domain.tax.dto.tax_sku_dto import TaxSkuDTO


class TaxSkuBuilder:

    @classmethod
    def build(cls, sku):
        attributes = [SkuAttributeDTO(key=attr["key"], value=attr["value"]) for attr in sku["attributes"]]
        prices = []
        for price in sku["prices"]:
            pretax_price = price.get("pretax_price")
            posttax_price = price.get("posttax_price") if not pretax_price else None
            prices.append(SkuPriceDTO(date=price["date"], index=price["index"], pretax_price=pretax_price,
                                      posttax_price=posttax_price, is_pre_discount_price=price['is_pre_discount_price'])
                          )
        return TaxSkuDTO(attributes=attributes, category_id=sku["category_id"], index=sku["index"], prices=prices)
