from distutils.util import strtobool

from marshmallow import Schema, fields, validates_schema, ValidationError, post_load
from marshmallow.validate import OneOf

from taxation.domain.tax.dto.sku_attributes_dto import AttributeKey


class AttributesSchema(Schema):
    key = fields.String(required=True, allow_none=False, validate=[OneOf(AttributeKey.all())])
    value = fields.String(required=True, allow_none=True)

    @validates_schema
    def validate_value(self, data):
        if data["key"] in [AttributeKey.HOTEL_ID.value, AttributeKey.SELLER_ID.value]:
            if not data["value"]:
                raise ValidationError("{} cannot be null".format(data["key"].value))

    @post_load
    def process_attribute(self, data):
        data["key"] = AttributeKey(data["key"])
        if data["key"] in [AttributeKey.SELLER_HAS_LUT, AttributeKey.BUYER_HAS_LUT, AttributeKey.IS_BUYER_IN_SEZ]:
            try:
                data["value"] = bool(strtobool(data["value"])) if data["value"] else False
            except ValueError:
                raise ValidationError("Unsupported value for attribute {}".format(data["key"].value))


class PricesSchema(Schema):
    date = fields.Date(required=True, allow_none=False)
    index = fields.String(required=False, allow_none=True)
    is_pre_discount_price = fields.Boolean(required=False, allow_none=False, missing=False)
    pretax_price = fields.Decimal(required=False, allow_none=True, as_string=True)
    posttax_price = fields.Decimal(required=False, allow_none=True, as_string=True)

    @validates_schema
    def validate_object(self, data):
        if bool(data.get("pretax_price") is None) == bool(data.get("posttax_price") is None):
            raise ValidationError("Need exactly one of pretax_price or posttax_price")


class SkuSchema(Schema):
    attributes = fields.Nested(AttributesSchema, many=True, required=True)
    category_id = fields.String(required=True, allow_none=False)
    index = fields.String(required=False, allow_none=True)
    prices = fields.Nested(PricesSchema, required=True, allow_none=False, many=True)

    @validates_schema
    def validate_object(self, data):
        attribute_keys = [a.get("key") for a in data["attributes"]]
        if bool(AttributeKey.HOTEL_ID in attribute_keys) == bool(AttributeKey.SELLER_ID in attribute_keys):
            raise ValidationError("Need exactly one of hotel_id or seller_id attribute")


class CalculateTaxV5Schema(Schema):
    skus = fields.Nested(SkuSchema, many=True, required=True, allow_none=False)


