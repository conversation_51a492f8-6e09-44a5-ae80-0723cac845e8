import logging

from flask import Blueprint

from taxation.api.request_parsers import raw_json
from taxation.api.v5.request_schema import CalculateTaxV5Schema
from taxation.api.v5.response_schema import ResponseSkuSchema, TaxConfigSchema
from taxation.decorators import json_response
from taxation.domain.tax.services.tax_v5_api_service import TaxV5ApiService

bp = Blueprint("tax_v5", __name__)

logger = logging.getLogger(__name__)
request_logger = logging.getLogger('request_handler')


@bp.route("/calculate_tax", methods=['POST'])
@raw_json(CalculateTaxV5Schema)
@json_response()
def calculate_tax_api(parsed_request):
    """
     This Tax API computes tax for multi-tenant system
    ---
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: This Tax API computes tax for multi-tenant system
        tags:
            - MultiTenant Tax
        parameters:
            - in: body
              name: body
              description:
              required: True
              schema: CalculateTaxV5Schema
        responses:
            200:
                description: Tax computation response
                schema: ResponseSkuSchema
    """
    tax_sku_aggregates = TaxV5ApiService().compute_tax(parsed_request["skus"])
    response = ResponseSkuSchema().dump(tax_sku_aggregates, many=True)
    return dict(skus=response.data), 200


@bp.route("/<string:property_id>/tax_configs", methods=['GET'])
@json_response()
def get_tax_configs_api(property_id):
    """
    Get Tax Configurations for a given property
    ---
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Fetches the tax configurations for the given property ID
        tags:
            - Tax Configurations
        parameters:
            - in: path
              name: property_id
              description: The property ID for which to fetch tax configurations
              required: True
              type: string
        responses:
            200:
                description: Tax configurations retrieved successfully
                schema:
                    type: array
                    items:
                        $ref: '#/definitions/TaxConfigSchema'
            404:
                description: Property not found
    """
    tax_configs = TaxV5ApiService().get_tax_configs_for_property(property_id)
    response = TaxConfigSchema().dump(tax_configs, many=True)
    return dict(tax_configs=response.data), 200
