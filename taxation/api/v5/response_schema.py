from marshmallow import fields, Schema


class TaxBreakupSchema(Schema):
    tax_type = fields.String(required=True, allow_none=False)
    tax_value = fields.Decimal(required=True, allow_none=False)
    tax_code = fields.String(required=True, allow_none=False)
    tax_amount = fields.Decimal(required=True, allow_none=False, as_string=True)


class PricesSchema(Schema):
    date = fields.Date(required=True, allow_none=False)
    index = fields.String(required=False, allow_none=True)
    is_pre_discount_price = fields.Boolean(required=False, allow_none=False, missing=False)
    pretax_price = fields.Decimal(required=False, allow_none=True, as_string=True)
    taxable_amount = fields.Decimal(required=False, allow_none=True, as_string=True)
    posttax_price = fields.Decimal(required=False, allow_none=True, as_string=True)
    tax_amount = fields.Decimal(required=True, allow_none=False, as_string=True)
    tax_breakup = fields.Nested(TaxBreakupSchema, required=True, allow_none=False, many=True)


class ResponseSkuSchema(Schema):
    index = fields.String(required=False, allow_none=True)
    prices = fields.Nested(PricesSchema, required=True, allow_none=False, many=True)


class DateRangeSchema(Schema):
    higher = fields.String(required=True)
    lower = fields.String(required=True)


class PreTaxRangeSchema(Schema):
    higher = fields.String(required=True)
    lower = fields.String(required=True)


class TaxConfigSchema(Schema):
    tax_config_id = fields.Integer(required=True)
    tax_code = fields.String(required=True)
    service_category = fields.String(required=True)
    tax_type = fields.String(required=True)
    date_range = fields.Nested(DateRangeSchema, required=True)
    pretax_range = fields.Nested(PreTaxRangeSchema, required=True)
    tax_value = fields.Decimal(as_string=True, required=True)
    included_in_rate = fields.Boolean(allow_none=True)
