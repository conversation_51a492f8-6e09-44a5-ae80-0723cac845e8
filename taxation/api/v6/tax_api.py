import logging

from flask import Blueprint

from taxation.api.request_parsers import raw_json
from taxation.api.v6.request_schema import CalculateTaxV6Schema
from taxation.api.v6 import ResponseSkuSchema
from taxation.decorators import json_response
from taxation.domain.tax.services.tax_v6_api_service import TaxV6ApiService

bp = Blueprint("tax_v6", __name__)

logger = logging.getLogger(__name__)
request_logger = logging.getLogger('request_handler')


@bp.route("/calculate_tax", methods=['POST'])
@raw_json(CalculateTaxV6Schema)
@json_response()
def calculate_tax_api(parsed_request):
    """
     This Tax API computes tax for multi-tenant system
     This doesn't support taxable amount rule, seller_ids and flat tax type
    ---
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: This Tax API computes tax for multi-tenant system
        tags:
            - MultiTenant Tax
        parameters:
            - in: body
              name: body
              description:
              required: True
              schema: CalculateTaxV6Schema
        responses:
            200:
                description: Tax computation response
                schema: ResponseSkuSchema
    """
    tax_sku_aggregates = TaxV6ApiService().compute_tax(parsed_request["skus"])
    response = ResponseSkuSchema().dump(tax_sku_aggregates, many=True)
    return dict(skus=response.data), 200
