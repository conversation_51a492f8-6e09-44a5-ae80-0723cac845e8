import os

import pytest
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from taxation.app import create_app
from object_registry import finalize_app_initialization

dir_path = os.path.dirname(os.path.abspath(__file__))


def setup_app_for_test():
    _app = create_app()
    finalize_app_initialization(_app)
    return _app


@pytest.fixture(scope="session", autouse=True)
def app():
    _app = setup_app_for_test()
    ctx = _app.test_request_context()
    ctx.push()

    print("===========> Dropping and re-creating tables")
    Base.metadata.drop_all(bind=db_engine.get_engine(None))
    Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("=====>>>> Created tables")

    print("===========> Using app fixture")
    yield _app

    print("===========> Teardown app fixture")

    ctx.pop()


@pytest.fixture(scope="session", autouse=True)
def seed_data():
    print("====> seed_data running")

    with open(dir_path + '/integration_tests/resources/seed_data.sql', 'r') as s:
        db_engine.get_session().execute(s.read())
    print("Data seeded")
    db_engine.get_session().commit()


@pytest.fixture(scope="session")
def client_(app, request):
    # test_client = app_.test_client()

    with app.test_client() as client:
        yield client

    def teardown():
        pass  # databases and resourses have to be freed at the end. But so far we don't have anything

    request.addfinalizer(teardown)
    return client
