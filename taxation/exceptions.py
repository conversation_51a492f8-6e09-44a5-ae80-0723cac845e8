class TaxationBaseException(Exception):
    def __init__(self, error, context=None):
        self.message = error.message
        self.error_code = error.code
        self.context = context
        super().__init__(error.message)


class InvalidRequestData(TaxationBaseException):
    def __init__(self, error, context=None):
        super().__init__(error, context)


class TaxConfigurationError(TaxationBaseException):
    def __init__(self, error, context=None):
        super().__init__(error, context)


class ResourceNotFound(TaxationBaseException):
    def __init__(self, error, context=None):
        super().__init__(error, context)


class InvalidProductType(TaxationBaseException):
    def __init__(self, error, context=None):
        super().__init__(error, context)


class HxClientException(Exception):
    def __init__(self, message):
        self.message = message
        super().__init__(message)


class MultipleResultsFound(Exception):
    def __init__(self, error, context=None):
        super().__init__(error, context)
