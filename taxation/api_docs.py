import inspect

import marshmallow
from apispec import APISpec
from flasgger import Swagger

from taxation.api.v1.cache_api import refresh_cache
from taxation.api.v1.tax_api import calculate_tax_api, calculate_pretax_api
from taxation.api.v3 import schema
from taxation.api.v3.tax_api import calculate_tax_api as calculate_tax_api_v3, reverse_tax_api as reverse_tax_api_v3
from taxation.api.v4.tax_api import calculate_tax_api as calculate_tax_api_v4
from taxation.api.v5.tax_api import calculate_tax_api as calculate_tax_api_v5
from taxation.api.v5.tax_api import get_tax_configs_api as get_tax_configs_api
from taxation.api.v6.tax_api import calculate_tax_api as calculate_tax_api_v6


def setup_schema_definition(spec):
    for name, obj in inspect.getmembers(schema):
        if inspect.isclass(obj) and type(obj) == marshmallow.schema.SchemaMeta:
            spec.definition(name, schema=obj)


def setup_path(spec):
    # define paths
    spec.add_path(view=calculate_tax_api)
    spec.add_path(view=calculate_pretax_api)
    spec.add_path(view=calculate_tax_api_v4)
    spec.add_path(view=calculate_tax_api_v3)
    spec.add_path(view=reverse_tax_api_v3)
    spec.add_path(view=calculate_tax_api_v5)
    spec.add_path(view=get_tax_configs_api)
    spec.add_path(view=calculate_tax_api_v6)
    spec.add_path(view=refresh_cache)


def init_docs(app):
    ctx = app.test_request_context()
    ctx.push()

    # Create an APISpec
    spec = APISpec(
        title='Taxation API Docs',
        version='1.0.0',
        plugins=[
            'apispec.ext.flask',
            'apispec.ext.marshmallow',
        ],
    )

    swagger_config = {
        "headers": [
        ],
        "specs": [
            {
                "endpoint": 'apispec_1',
                "route": '/tax/apispec_1.json',
                "rule_filter": lambda rule: True,  # all in
                "model_filter": lambda tag: True,  # all in
            }
        ],
        "static_url_path": "/tax/flasgger_static",
        # "static_folder": "static",  # must be set by user
        "swagger_ui": True,
        "specs_route": "/tax/apidocs/"
    }

    setup_schema_definition(spec)
    setup_path(spec)
    sw = Swagger(template=spec.to_dict(), config=swagger_config)
    sw.init_app(app)
