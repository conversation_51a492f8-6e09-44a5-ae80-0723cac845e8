import functools
import json
import logging

from flask import jsonify, make_response
from treebo_commons.request_tracing.flask import before_request, after_request

from taxation.globals import consumer_context

logger = logging.getLogger(__name__)


def json_response():
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Unpacking values on LHS (*status_code), works only in Python 3. This will break in Python 2
            response, *status_code = func(*args, **kwargs)
            status_code = status_code[0] if status_code else 200
            if isinstance(response, dict):
                resp = make_response(jsonify(response), status_code)
            else:
                resp = json.dumps(response)
            return resp

        return wrapper

    return decorator


def consumer_middleware(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        """
        Wrapper
        :param args:
        :param kwargs:
        :return:
        """
        from flask import current_app

        with current_app.test_request_context():
            from flask import request
            # TODO: Any better way to set tenant_id. May be better to move this out of treebo_commons?
            # TODO: In treebo_commons, figure out a better way to set context for background jobs, instead of from
            #  request headers
            request_headers = dict(request.headers)
            request_headers['X-Tenant-Id'] = consumer_context.tenant_id
            before_request(request_headers)
            r_val = func(*args, **kwargs)
            after_request(response=None, request=None)
            return r_val

    return wrapper