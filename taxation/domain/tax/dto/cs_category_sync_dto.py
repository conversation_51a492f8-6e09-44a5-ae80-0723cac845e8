class CSCategorySyncDTO(object):
    # {'hsn_sac': 'sdfsdf', 'code': 'service-13', 'id': 3, 'status': 'ACTIVE', 'name': 'service-1'}
    # service_accounting_code, code, name
    def __init__(self, data):
        # service_accounting_code is the sac/hsn code
        self.service_accounting_code = data['hsn_sac'].strip()
        # code is service code/category id
        self.code = data['code'].strip()
        self.name = data['name'].strip()

    def __str__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__
