from typing import List

from taxation.domain.tax.dto.sku_attributes_dto import SkuAttributeDTO
from taxation.domain.tax.dto.sku_price_dto import SkuPriceDTO


class TaxSkuDTO:
    def __init__(self, attributes: List[SkuAttributeDTO], category_id, index, prices: List[SkuPriceDTO]):
        self._attributes = attributes
        self.category_id = category_id
        self.index = index
        self.prices = prices

    @property
    def attributes(self):
        return {attr.key: attr.value for attr in self._attributes}

