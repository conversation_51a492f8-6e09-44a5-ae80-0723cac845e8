from typing import List


class SkuPrice(object):
    def __init__(self, date, index, sku_price):
        self.date = date
        self.index = index
        self.sku_price = sku_price
        self.tax = None
        self.pretax_price = None
        self.posttax_price = None

    @property
    def pre_tax_price(self):
        return self.pretax_price

    @property
    def post_tax_price(self):
        return self.posttax_price

    def update_tax_details(self, tax_details, pretax_price, posttax_price):
        self.tax = tax_details
        self.pretax_price = pretax_price
        self.posttax_price = posttax_price


class Sku(object):
    def __init__(self, index, prices: List[SkuPrice], attributes, sku_code=None, sku_category_code=None):
        self.index = index
        self.prices = prices
        self.attributes = attributes
        self.sku_code = sku_code
        self.sku_category_code = sku_category_code
        # This value will be populated while processing the request, using sku_code or sku_category_code
        self.service = None
        self.state_id = self.attributes.get("state_id", None)

    def get_hotel_id(self):
        hotel_id = self.attributes.get("hotel_id", None)
        return hotel_id

    def get_state_id(self):
        return self.state_id


class BookingDto(object):
    def __init__(self, skus: List[Sku], is_sez, buyer_has_lut, seller_has_lut, gstin):
        self.skus = skus
        self.is_sez = is_sez
        self.buyer_has_lut = buyer_has_lut
        self.seller_has_lut = seller_has_lut
        self.gstin = gstin

    def get_hotel_ids(self):
        return list({sku.get_hotel_id() for sku in self.skus if sku.get_hotel_id() is not None})

    def get_state_ids(self):
        return {sku.get_state_id() for sku in self.skus if sku.get_state_id() is not None}

    def get_sku_codes(self):
        return list({sku.sku_code for sku in self.skus if sku.sku_code})

    def enrich(self, services, sku_code_to_category_map, hotel_to_state_map=None, cs_state_id_to_state_map=None):
        category_id_to_service_map = {service.category_id: service for service in services}

        for sku in self.skus:
            if sku.sku_code:
                sku.sku_category_code = sku_code_to_category_map.get(sku.sku_code)

            if not sku.state_id:
                sku.state_id = hotel_to_state_map.get(sku.get_hotel_id())
            else:
                # Previous state_id in SKU is cs_state_id, as received in API.
                # Changing it to Tax DB state_id, as that is what is used in other cases, when hotel_id is passed
                # TODO: Ideally we should fix tax service and database models to work just with catalog state_id
                sku.state_id = cs_state_id_to_state_map.get(sku.state_id)
            sku.service = category_id_to_service_map.get(sku.sku_category_code)

    def get_service_ids(self):
        return {sku.service.id for sku in self.skus}
