class PropertySKUSyncDTO(object):
    def __init__(self, property_id, sku_code, status):
        self.property_id = property_id
        self.sku_code = sku_code
        self.status = status

    def __str__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @classmethod
    def get_property_sku_sync_dto_from_cs_data(cls, sku_configs):
        sku_dto_list = []
        for sku_config in sku_configs:
            sku_dto_list.append(PropertySKUSyncDTO(sku_config['property_id'], sku_config['sku_code'],
                                                   sku_config['status']))
        return sku_dto_list