from taxation.domain.tax.dto.property_room_sync_dto import PropertyRoomSyncDto
from taxation.domain.tax.dto.property_sku_sync_dto import PropertySKUSyncDTO


class PropertySyncDto(object):
    def __init__(self, hx_id, cs_hotel_id, state_id, state_name, status, name, hotel_id=None,
                 room_type_configs=None, sku_configs=None):
        self.hx_id = hx_id
        self.hotel_name = name
        self.state_id = state_id
        self.state_name = state_name
        self.cs_hotel_id = cs_hotel_id
        self.status = status
        self.hotel_id = hotel_id
        self.property_room_dtos = PropertyRoomSyncDto.get_property_room_sync_dto_from_cs_data(cs_hotel_id,
                                                                                              room_type_configs)
        self.property_sku_dtos = PropertySKUSyncDTO.get_property_sku_sync_dto_from_cs_data(sku_configs)

    def __str__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__
