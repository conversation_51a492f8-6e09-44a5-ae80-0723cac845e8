class SellerDto(object):
    def __init__(self, seller_id, name, seller_category_id, state_id, status):
        self.seller_id = seller_id
        self.name = name
        self.seller_category_id = seller_category_id
        self.state_id = state_id
        self.status = status

    @staticmethod
    def create_from_catalog_data(catalog_seller_detail_data):
        data = catalog_seller_detail_data
        return SellerDto(
            seller_id=data.get('seller_id'),
            name=data.get('name'),
            seller_category_id=data.get('seller_category_id'),
            state_id=data.get('city').get('state').get('id'),
            status=data.get('status'),
        )
