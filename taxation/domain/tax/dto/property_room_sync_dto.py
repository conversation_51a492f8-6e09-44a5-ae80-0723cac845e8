class PropertyRoomSyncDto(object):
    def __init__(self, cs_hotel_id, name, room_code):
        self.cs_hotel_id = cs_hotel_id
        self.name = name
        self.room_code = room_code

    def __str__(self):
        return str(self.__dict__)

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    @classmethod
    def get_property_room_sync_dto_from_cs_data(cls, cs_property_id, room_type_configs):
        dto_list = []
        for room_type_config in room_type_configs:
            dto_list.append(PropertyRoomSyncDto(cs_property_id, room_type_config['room_type']['type'],
                                                room_type_config['room_type']['code']))
        return dto_list
