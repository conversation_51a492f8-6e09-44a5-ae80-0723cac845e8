from decimal import ROUND_HALF_EVEN, Decimal
from typing import List

from taxation.domain.tax.dto.tax_breakup_dto import TaxBreakupDTO
from taxation.domain.tax.entities.v5.tax_config import TaxableAmountRuleEntity


class SkuPriceDTO:
    def __init__(self, date, index, posttax_price=None, pretax_price=None, tax_breakup: List[TaxBreakupDTO] = None,
                 is_pre_discount_price=False):
        self.date = date
        self.index = index
        self._posttax_price = posttax_price
        self._pretax_price = pretax_price
        self._tax_breakup = tax_breakup if tax_breakup else []
        self.has_pretax_price = False if self._pretax_price is None else True
        self.has_posttax_price = False if self._posttax_price is None else True
        if self.has_pretax_price:
            self._taxable_amount = self._pretax_price
        else:
            self._taxable_amount = None
        self.is_pre_discount_price = is_pre_discount_price

    def round(self, amount):
        return amount.quantize(Decimal('.01'), rounding=ROUND_HALF_EVEN)

    def update_taxable_amount(self, taxable_amount_rule: TaxableAmountRuleEntity):
        if not taxable_amount_rule:
            return
        if not self.has_pretax_price:
            return

        self._taxable_amount = self._taxable_amount * taxable_amount_rule.percentage_of_total_value / Decimal('100')
        if self._taxable_amount < taxable_amount_rule.minimum_taxable_amount:
            self._taxable_amount = taxable_amount_rule.minimum_taxable_amount

    @property
    def posttax_price(self):
        if self.has_posttax_price:
            return self.round(self._posttax_price)
        return self.round(self._taxable_amount + self.tax_amount)

    @property
    def pretax_price(self):
        if self.has_pretax_price:
            return self.round(self._pretax_price)
        return self.round(self._posttax_price - self.tax_amount)

    @property
    def taxable_amount(self):
        if self.has_pretax_price:
            return self.round(self._taxable_amount)
        return self.round(self._posttax_price - self.tax_amount)

    @pretax_price.setter
    def pretax_price(self, value):
        self._pretax_price = value
        self._taxable_amount = value

    @property
    def tax_amount(self):
        return self.round(sum([tax_item.tax_amount for tax_item in self.tax_breakup], Decimal('0')))

    @property
    def tax_breakup(self):
        return self._tax_breakup

    @tax_breakup.setter
    def tax_breakup(self, value):
        self._tax_breakup = value
