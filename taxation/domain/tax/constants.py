from enum import Enum


class BaseEnum(Enum):
    @classmethod
    def all(cls, exclude=None):
        if exclude:
            return [enum.value for enum in cls if enum not in exclude]
        else:
            return [enum.value for enum in cls]

    @classmethod
    def all_options(cls, as_set=False):
        return [enum for enum in cls] if not as_set else {enum for enum in cls}

    def __str__(self):
        return self.value

    @property
    def label(self):
        return self.value

    @classmethod
    def get_enums(cls, user_type):
        return [enum for enum in cls]


class AttributeKey(BaseEnum):
    HOTEL_ID = "hotel_id"
    SELLER_ID = "seller_id"
    BUYER_GSTIN = "buyer_gstin"
    IS_BUYER_IN_SEZ = "is_buyer_in_sez"
    BUYER_HAS_LUT = "buyer_has_lut"
    SELLER_HAS_LUT = "seller_has_lut"

    @property
    def list(self):
        return list(map(lambda a: a.value, AttributeKey.__members__))
