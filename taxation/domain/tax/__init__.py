from taxation.domain.tax.repositories.global_config_repository import GlobalConfigRepository
from taxation.domain.tax.repositories.gst_repository import GSTRepository
from taxation.domain.tax.repositories.hotel_config_repository import HotelConfigRepository
from taxation.domain.tax.repositories.room_config_repository import RoomConfigRepository
from taxation.domain.tax.repositories.seller_repository import SellerRepository
from taxation.domain.tax.repositories.service_repository import ServiceRepository
from taxation.domain.tax.repositories.sku_code_and_service_name_repository import SkuCodeAndServiceNameRepository
from taxation.domain.tax.repositories.sku_mapping_repository import SkuMappingRepository
from taxation.domain.tax.repositories.state_repository import StateRepository
from taxation.domain.tax.repositories.tax_config_repository import TaxConfigRepository
from taxation.domain.tax.repositories.tax_repository import TaxRepository


class RepositoryProvider(object):
    hotel_config_repository = HotelConfigRepository()
    state_repository = StateRepository()
    service_repository = ServiceRepository()
    gst_repository = GSTRepository(service_repository)
    global_config_repository = GlobalConfigRepository()
    room_config_repository = RoomConfigRepository()
    sku_mapping_repository = SkuMappingRepository()
    sku_service_name_mapping_repository = SkuCodeAndServiceNameRepository()
    seller_repository = SellerRepository()
    tax_config_repository = TaxConfigRepository()
    tax_repository = TaxRepository()


repo_provider = RepositoryProvider()
