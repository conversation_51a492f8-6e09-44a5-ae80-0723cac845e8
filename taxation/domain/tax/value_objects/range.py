from decimal import Decimal


class Range:
    def __init__(self, lower, higher):
        self._lower = lower
        self._higher = higher

    @property
    def lower(self):
        return self._lower if self._lower else 0

    @property
    def higher(self):
        return self._higher if self._higher else Decimal('Infinity')

    def contains(self, value):
        if isinstance(value, Decimal):
            if value == Decimal('Infinity'):
                return value == self.higher
            return self.lower <= value < self.higher
        if self._higher and self._lower:
            return self._lower <= value < self._higher
        elif self._lower:
            return self._lower <= value
        elif self._higher:
            return value < self._higher
        return True

    def __str__(self):
        return "[{l},{h})".format(l=self.lower, h=self.higher)

    def __eq__(self, other):
        if isinstance(other, Range):
            return self.lower == other.lower and self.higher == other.higher
        return False

    def __hash__(self):
        return hash((self.lower, self.higher))
