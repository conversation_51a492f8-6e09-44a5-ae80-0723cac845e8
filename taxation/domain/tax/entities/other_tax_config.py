from decimal import Decimal
from typing import List

from taxation import listutils
from taxation.domain.tax.entities.tax import TaxBreakup
from taxation.listutils import value_in_range
from taxation.models import OtherTax
from taxation.utils.stateful_utils import is_kerala_gstin


class OtherTaxConfig(object):
    def __init__(self, other_tax):
        self.other_tax = other_tax
        self.other_tax_dict = listutils.convert_to_multivalue_dict(self.other_tax, key=lambda item: item.state_id)

    def get_other_tax(self, state_id, service_id) -> List[OtherTax]:
        other_tax = self.other_tax_dict.get(state_id, list())
        filtered_other_tax = [x for x in other_tax if x.service_id == service_id]
        return filtered_other_tax

    def get_first_matching_config(self, price, date, state_id, service_id):
        return next(filter(lambda ot: (ot.effective_date <= date <= ot.expiry_date and
                                       ot.from_price_pretax <= price <= ot.to_price_pretax and
                                       ot.state_id == state_id and ot.service_id == service_id),
                           self.other_tax), None)

    def compute_tax(self, date, sku_price, service_id, state_id, gstin):
        if is_kerala_gstin(gstin):
            return None

        other_tax_item = self.get_first_matching_config(sku_price, date, state_id, service_id)
        if not other_tax_item:
            return None

        tax_type = other_tax_item.tax_type_name
        other_tax_percent = other_tax_item.tax_percent
        other_tax_value = round((sku_price * other_tax_percent) / Decimal("100.00"), 2)
        return TaxBreakup(type=tax_type, value=other_tax_value, percent=other_tax_percent)

    def filter(self, date, state_id, service_id):
        return [other_tax for other_tax in self.other_tax if
                value_in_range(date, other_tax.effective_date, other_tax.expiry_date) and
                other_tax.state_id == state_id and other_tax.service_id == service_id]
