from decimal import Decimal
from typing import List

from taxation import listutils
from taxation.domain.tax.entities.tax import TaxBreakup
from taxation.models import CGST
from taxation.numberutils import ZERO


class CgstTaxConfig(object):
    def __init__(self, cgst):
        self.cgst = cgst
        self.cgst_dict = listutils.convert_to_multivalue_dict(self.cgst, key=lambda item: item.service_id)

    def get_cgst(self, service_id) -> List[CGST]:
        return self.cgst_dict.get(service_id, list())

    def get_first_matching_config(self, price, date, service_id):
        return next(filter(lambda cgst: (cgst.effective_date <= date <= cgst.expiry_date and
                                         cgst.from_price_pretax <= price <= cgst.to_price_pretax and
                                         cgst.service_id == service_id),
                           self.cgst), None)

    def compute_tax(self, date, sku_price, service_id, state_id, gstin):
        cgst_item = self.get_first_matching_config(sku_price, date, service_id)
        cgst_percent = cgst_item.cgst_percent if cgst_item else ZERO
        cgst_value = round((sku_price * cgst_percent) / Decimal("100.00"), 2) if cgst_item else ZERO
        return TaxBreakup(type="cgst", value=cgst_value, percent=cgst_percent)
