from decimal import Decimal
from typing import List

from taxation import listutils
from taxation.domain.tax.entities.tax import TaxBreakup
from taxation.listutils import value_in_range
from taxation.models import SGST
from taxation.numberutils import ZERO


class SgstTaxConfig(object):
    def __init__(self, sgst):
        self.sgst = sgst
        self.sgst_dict = listutils.convert_to_multivalue_dict(self.sgst, key=lambda item: item.state_id)

    def get_sgst(self, state_id, service_id) -> List[SGST]:
        sgst = self.sgst_dict.get(state_id, list())
        filtered_sgst = [x for x in sgst if x.service_id == service_id]
        return filtered_sgst

    def get_first_matching_config(self, price, date, state_id, service_id):
        return next(filter(lambda sgst: (sgst.effective_date <= date <= sgst.expiry_date and
                                         sgst.from_price_pretax <= price <= sgst.to_price_pretax and
                                         sgst.state_id == state_id and sgst.service_id == service_id),
                           self.sgst), None)

    def compute_tax(self, date, sku_price, service_id, state_id, gstin):
        sgst_item = self.get_first_matching_config(sku_price, date, state_id, service_id)
        sgst_percent = sgst_item.sgst_percent if sgst_item else ZERO
        sgst_value = round((sku_price * sgst_percent) / Decimal("100.00"), 2) if sgst_item else ZERO
        return TaxBreakup(type="sgst", value=sgst_value, percent=sgst_percent)

    def filter(self, date, state_id, service_id):
        return [sgst for sgst in self.sgst if value_in_range(date, sgst.effective_date, sgst.expiry_date) and
                sgst.state_id == state_id and sgst.service_id == service_id]
