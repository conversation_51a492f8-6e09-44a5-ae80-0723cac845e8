from typing import List

from taxation.models.tax_model import Weekdays, TaxType
from taxation.domain.tax.value_objects.range import Range


class TaxConfigV5:
    def __init__(self, tax_config_id, tax_id, tax_code, service_category, tax_type: TaxType, date_range: Range, pretax_range: Range,
                 tax_value, included_in_rate, weekdays: List[Weekdays], posttax_range: Range = None):
        self.tax_config_id = tax_config_id
        self.tax_id = tax_id
        self.tax_code = tax_code
        self.service_category = service_category
        self.tax_type = tax_type
        self.date_range = date_range
        self.pretax_range = pretax_range
        self.posttax_range = posttax_range
        self.tax_value = tax_value
        self.included_in_rate = included_in_rate
        self.weekdays = weekdays

    def is_applicable(self, date=None, pretax_price=None, posttax_price=None):
        if date and not self.date_range.contains(date):
            return False
        if date and Weekdays[date.strftime('%A').upper()] not in self.weekdays:
            return False
        if pretax_price is not None and not self.pretax_range.contains(pretax_price):
            return False
        if posttax_price is not None and not self.posttax_range.contains(posttax_price):
            return False
        return True

    def evaluate_tax_from_pretax(self, pretax_amount):
        if self.tax_type == TaxType.FLAT:
            return self.tax_value
        elif self.tax_type == TaxType.PERCENTAGE:
            return (pretax_amount * self.tax_value) / 100


class TaxableAmountRuleEntity:
    def __init__(self, rule_id, service_category_id, percentage_of_total_value, minimum_taxable_amount):
        self.rule_id = rule_id
        self.service_category_id = service_category_id
        self.percentage_of_total_value = percentage_of_total_value
        self.minimum_taxable_amount = minimum_taxable_amount
