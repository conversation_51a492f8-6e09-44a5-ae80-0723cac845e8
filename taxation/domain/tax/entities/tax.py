from typing import List

from taxation.numberutils import ZERO


class TaxBreakup(object):
    def __init__(self, type, value, percent):
        self.type = type
        self.value = value
        self.percent = percent

    def dict(self):
        return dict(type=self.type, value=self.value, percent=self.percent)

    def __str__(self):
        return str(self.dict())


class Tax(object):
    def __init__(self, tax_breakups: List[TaxBreakup], is_sez=False, buyer_has_lut=False, seller_has_lut=False):
        tax_breakups = [breakup for breakup in tax_breakups if breakup is not None]
        tax_breakup_by_type = {tax_breakup.type: tax_breakup for tax_breakup in tax_breakups}

        if "igst" not in tax_breakup_by_type:
            igst_value, igst_percent = ZERO, ZERO
            if is_sez:
                cgst, sgst = tax_breakup_by_type.get("cgst"), tax_breakup_by_type.get("sgst")
                tax_breakup_by_type["cgst"] = TaxBreakup(type="cgst", value=ZERO, percent=ZERO)
                tax_breakup_by_type["sgst"] = TaxBreakup(type="sgst", value=ZERO, percent=ZERO)

                igst_value = cgst.value + sgst.value
                igst_percent = cgst.percent + sgst.percent
                if buyer_has_lut and seller_has_lut:
                    igst_value, igst_percent = ZERO, ZERO
            tax_breakup_by_type["igst"] = TaxBreakup(type="igst", value=igst_value, percent=igst_percent)

        self.tax_breakup_by_type = tax_breakup_by_type

    def details(self):
        return dict(breakup=self.breakup(), percent=self.total_tax_percent(), value=self.total_tax())

    def total_tax(self):
        return sum(tax_breakup.value for tax_type, tax_breakup in self.tax_breakup_by_type.items())

    def total_tax_percent(self):
        return sum(tax_breakup.percent for tax_type, tax_breakup in self.tax_breakup_by_type.items())

    def breakup(self):
        return [breakup.dict() for breakup in self.tax_breakup_by_type.values()]

    def __str__(self):
        return str(self.__dict__)
