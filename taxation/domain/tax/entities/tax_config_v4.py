from taxation.domain.tax.entities.tax import Tax
from taxation.domain.tax.entities.tax_config import TaxConfig


class TaxConfigV4(TaxConfig):
    def __init__(self, cgst, sgst, igst, other_tax):
        super(TaxConfigV4, self).__init__(cgst, sgst, igst, other_tax)

    def calculate_tax(self, tax_request, hotel_state_dict, sku_code_and_category_map):
        """

        :param tax_request: Object from TaxRequestSchemaV4
        :param hotel_state_dict:
        :param sku_code_and_category_map:
        :return:
        """

        def read_hotel_id_from_attribute(sku):
            attributes = sku['attributes']
            hotel_id = [attr['value'] for attr in attributes if attr['key'] == 'hotel_id']
            return hotel_id[0] if hotel_id else None

        def build_price_dict(date, sku_price, index, tax: Tax):
            return dict(
                index=index,
                date=str(date),
                pre_tax_price=sku_price,
                post_tax_price=sku_price + tax.total_tax(),
                tax=[
                    dict(
                        breakup=tax.breakup(),
                        index=index,
                        percent=tax.total_tax_percent(),
                        value=tax.total_tax()
                    )]
            )

        # Compute Tax
        response_data = dict(skus=[])
        for sku in tax_request['skus']:
            index = sku['index']
            hotel_id = read_hotel_id_from_attribute(sku)
            state_id = hotel_state_dict[hotel_id]
            sku_dict = dict(index=index, prices=[])

            for price in sku['prices']:
                for per_sku_code_price in price['sku_prices']:
                    sku_price = per_sku_code_price['sku_price']
                    service = sku_code_and_category_map[per_sku_code_price['sku_code']]

                    tax = self.compute_tax(price['date'], sku_price, state_id, service.id, sku["is_sez"],
                                           sku['buyer_has_lut'], sku['seller_has_lut'], sku['gstin'], reverse_tax=False)
                    price_dict = build_price_dict(price['date'], sku_price, per_sku_code_price['index'], tax)

                    sku_dict['prices'].append(price_dict)
            response_data['skus'].append(sku_dict)
        return response_data

