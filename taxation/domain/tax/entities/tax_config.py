import logging

from marshmallow.utils import ZERO

from taxation.domain.tax.dto.booking_dto import BookingDto
from taxation.domain.tax.entities.cgst_tax_config import CgstTaxConfig
from taxation.domain.tax.entities.other_tax_config import OtherTaxConfig
from taxation.domain.tax.entities.sgst_tax_config import SgstTaxConfig
from taxation.domain.tax.entities.tax import Tax, TaxBreakup
from taxation.listutils import value_in_range
from taxation.numberutils import ZERO
from taxation.utils.mathutils import compute_tax_from_posttax
from taxation.utils.stateful_utils import is_kerala_gstin

logger = logging.getLogger(__name__)
KEY_PREFIX = 'taxation_'


class TaxConfig(object):
    def __init__(self, cgst, sgst, igst, other_tax):
        """

        :param cgst: List of CGST objects loaded from redis cache and DB
        :param sgst: List of SGST objects loaded from redis cache and DB
        :param igst: List of IGST objects loaded from redis cache and DB
        :param other_tax: List of OtherTax objects loaded from redis cache and DB
        """
        self.cgst_config = CgstTaxConfig(cgst)
        self.sgst_config = SgstTaxConfig(sgst)
        self.other_tax_config = OtherTaxConfig(other_tax)
        self.igst = igst
        self.tax_configs = [self.cgst_config, self.sgst_config, self.other_tax_config]

    def partial_copy(self, state_id, service_id):
        cgst = self.cgst_config.get_cgst(service_id)
        sgst = self.sgst_config.get_sgst(state_id, service_id)
        other_tax = self.other_tax_config.get_other_tax(state_id, service_id)
        return TaxConfig(cgst=cgst, sgst=sgst, igst=[], other_tax=other_tax)

    def calculate_tax(self, booking: BookingDto, reverse_tax=False):
        for sku in booking.skus:
            state_id = sku.state_id

            for sku_price in sku.prices:
                tax = self.compute_tax(sku_price.date, sku_price.sku_price, state_id, sku.service.id,
                                       booking.is_sez, booking.buyer_has_lut, booking.seller_has_lut, booking.gstin,
                                       reverse_tax=reverse_tax)
                if reverse_tax:
                    pretax_price, posttax_price = sku_price.sku_price - tax.total_tax(), sku_price.sku_price
                else:
                    pretax_price, posttax_price = sku_price.sku_price, sku_price.sku_price + tax.total_tax()

                sku_price.update_tax_details(
                    tax_details=dict(breakup=tax.breakup(), percent=tax.total_tax_percent(), value=tax.total_tax()),
                    pretax_price=pretax_price, posttax_price=posttax_price)

    def compute_tax(self, date, sku_price, state_id, service_id, is_sez, buyer_has_lut, seller_has_lut, gstin,
                    reverse_tax=False):
        if not reverse_tax:
            return Tax(
                tax_breakups=[tax_config.compute_tax(date, sku_price, service_id, state_id, gstin) for tax_config in
                              self.tax_configs],
                is_sez=is_sez, buyer_has_lut=buyer_has_lut, seller_has_lut=seller_has_lut)
        else:
            return self._reverse_compute_tax(date, sku_price, state_id, service_id, is_sez, buyer_has_lut,
                                             seller_has_lut, gstin)

    def _reverse_compute_tax(self, date, sku_price, state_id, service_id, is_sez, buyer_has_lut, seller_has_lut, gstin):
        other_tax = None
        other_taxes = self.other_tax_config.filter(date, state_id, service_id) if not is_kerala_gstin(gstin) else []

        for sgst in self.sgst_config.filter(date, state_id, service_id):
            from_price_posttax, to_price_posttax = sgst.from_price_posttax, sgst.to_price_posttax
            # Get first matching other_tax configuration by comparing pretax price range.
            other_tax = next((ot for ot in other_taxes if
                              ot.from_price_pretax == sgst.from_price_pretax and ot.to_price_pretax ==
                              sgst.to_price_pretax), None)

            if other_tax:
                # Add posttax price of other tax to sgst to compare with booking price, since sgst posttax price
                # slab
                #  doesn't contain other tax posttax price share
                from_price_posttax = sgst.from_price_posttax + other_tax.from_price_posttax
                to_price_posttax = sgst.to_price_posttax + other_tax.to_price_posttax

            if value_in_range(sku_price, from_price_posttax, to_price_posttax):
                break
        else:
            sgst = self.sgst_config.get_first_matching_config(sku_price, date, state_id, service_id)
            if not is_kerala_gstin(gstin):
                other_tax = self.other_tax_config.get_first_matching_config(sku_price, date, state_id, service_id)

        other_tax_percent = other_tax.tax_percent if other_tax else ZERO
        sgst_percent = sgst.sgst_percent
        cgst_percent = sgst.gst_percent - sgst_percent
        igst_percent = ZERO

        if is_sez:
            igst_percent = cgst_percent + sgst_percent
            cgst_percent, sgst_percent = ZERO, ZERO
            if buyer_has_lut and seller_has_lut:
                igst_percent = ZERO

        cgst_value, sgst_value, igst_value, other_tax_value = compute_tax_from_posttax(
            sku_price, cgst_percent, sgst_percent, igst_percent, other_tax_percent)

        other_tax_type = other_tax.tax_type_name if other_tax else None
        tax_breakups = [
            TaxBreakup(type="cgst", value=cgst_value, percent=cgst_percent),
            TaxBreakup(type="sgst", value=sgst_value, percent=sgst_percent),
            TaxBreakup(type="igst", value=igst_value, percent=igst_percent)]

        if other_tax_type:
            tax_breakups.append(TaxBreakup(type=other_tax_type, value=other_tax_value, percent=other_tax_percent))

        return Tax(tax_breakups=tax_breakups, is_sez=is_sez, buyer_has_lut=buyer_has_lut, seller_has_lut=seller_has_lut)
