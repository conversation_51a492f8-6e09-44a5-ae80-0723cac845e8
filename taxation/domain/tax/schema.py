from marshmallow import (
    Schema, fields, validate
)


class CSPropertyNameSchema(Schema):
    new_name = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class CSPropertyStateSchema(Schema):
    id = fields.Integer(required=True)
    name = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class CSPropertyLocationSchema(Schema):
    state = fields.Nested(CSPropertyStateSchema, required=True, allow_none=False, many=False)


class CSPropertyRoomConfigTypeSchema(Schema):
    type = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    code = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class CSPropertyRoomConfigSchema(Schema):
    room_type = fields.Nested(CSPropertyRoomConfigTypeSchema, required=True, many=False)


class CSPropertySKUSchema(Schema):
    property_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    sku_code = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    sku_name = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    status = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class CSPropertyDataSchema(Schema):
    status = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    location = fields.Nested(CSPropertyLocationSchema, required=True, allow_none=False, many=False)
    name = fields.Nested(CSPropertyNameSchema, required=True, allow_none=False, many=False)
    room_type_configs = fields.Nested(CSPropertyRoomConfigSchema, required=True, many=True)
    skus = fields.Nested(CSPropertySKUSchema, required=True, many=True)


class CSPropertySchema(Schema):
    data = fields.Nested(CSPropertyDataSchema, required=True, allow_none=False)
    entity = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    operation_type = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    # hx_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    hx_id = fields.Str(required=True, allow_none=True)
    cs_property_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class PropertySyncWebSchema(Schema):
    hotel_id = fields.Int(required=True)
    state_id = fields.Int(required=True)
    hotel_name = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    hx_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    catalog_property_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    state_name = fields.Str(required=True, allow_none=True)


class CSPropertyRoomTypeSchema(Schema):
    id = fields.Integer(required=True)
    code = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    type = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class CSPropertyRoomDataSchema(Schema):
    room_type = fields.Nested(CSPropertyRoomTypeSchema, required=True, allow_none=False, many=False)


class CSPropertyRoomSchema(Schema):
    data = fields.Nested(CSPropertyRoomDataSchema, required=True, allow_none=False)
    entity = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    operation_type = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    cs_property_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    # hx_id = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    hx_id = fields.Str(required=True, allow_none=True)


class CSCategoryDataSchema(Schema):
    # {'hsn_sac': 'sdfsdf', 'code': 'service-13', 'id': 3, 'status': 'ACTIVE', 'name': 'service-1'}
    hsn_sac = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    code = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    # id = fields.Integer(required=True)
    status = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    name = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))


class CSCategorySchema(Schema):
    # {'data': {'hsn_sac': 'sdfsdf', 'code': 'service-13', 'id': 3, 'status': 'ACTIVE', 'name': 'service-1'},
    #  'entity': 'SKU_CATEGORY', 'operation_type': 'UPDATE'}
    data = fields.Nested(CSCategoryDataSchema, required=True, allow_none=False)
    entity = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))
    operation_type = fields.Str(required=True, allow_none=False, validate=validate.Length(min=1))

