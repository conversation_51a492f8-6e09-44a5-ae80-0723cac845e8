from decimal import Decimal
from typing import List

from taxation.api.v5.tax_sku_builder import TaxSkuBuilder
from taxation.domain.tax.dto.sku_attributes_dto import AttributeKey
from taxation.domain.tax.dto.sku_price_dto import SkuPriceDTO
from taxation.domain.tax.dto.tax_breakup_dto import TaxBreakupDTO
from taxation.domain.tax.entities.v5.tax_config import TaxConfigV5
from taxation.domain.tax.services.base_tax_service import BaseTaxService
from taxation.domain.tax.services.tax_applicability.tax_applicability_service import TaxApplicabilityService
from taxation.domain.tax.value_objects.range import Range
from taxation.models.tax_model import TaxType


class TaxV5ApiService(BaseTaxService):

    def __init__(self):
        super(TaxV5ApiService, self).__init__()

    def compute_tax(self, skus):
        tax_sku_dtos = [TaxSkuBuilder.build(sku) for sku in skus]
        hotel_ids = set()
        seller_ids = set()
        sku_category_ids = set()
        for tax_sku_dto in tax_sku_dtos:
            if tax_sku_dto.attributes.get(AttributeKey.HOTEL_ID):
                hotel_ids.add(tax_sku_dto.attributes.get(AttributeKey.HOTEL_ID))
            if tax_sku_dto.attributes.get(AttributeKey.SELLER_ID):
                seller_ids.add(tax_sku_dto.attributes.get(AttributeKey.SELLER_ID))
            sku_category_ids.add(tax_sku_dto.category_id)

        self.set_config_id_to_tax_config(sku_category_ids)

        hotel_id_to_tax_config_ids = self.get_hotel_id_to_tax_config_ids(hotel_ids)
        seller_id_to_tax_config_ids = self.seller_id_to_tax_config_ids(seller_ids)
        hotel_to_taxable_amount_rule_ids = self.hotel_to_taxable_amount_rule_ids(hotel_ids, sku_category_ids)

        for tax_sku_dto in tax_sku_dtos:
            attributes = tax_sku_dto.attributes
            hotel_id = attributes.get(AttributeKey.HOTEL_ID)
            seller_id = attributes.get(AttributeKey.SELLER_ID)
            taxable_amount_rule = None
            if hotel_id:
                tax_config_ids = hotel_id_to_tax_config_ids.get(hotel_id, [])
                taxable_amount_rule_ids = hotel_to_taxable_amount_rule_ids.get(hotel_id)

                if taxable_amount_rule_ids:
                    for rule in self.taxable_amount_rules:
                        if rule.rule_id in taxable_amount_rule_ids and rule.service_category_id == tax_sku_dto.category_id:
                            taxable_amount_rule = rule
                            break
            else:
                tax_config_ids = seller_id_to_tax_config_ids.get(seller_id, [])

            tax_configs = [tc for tc in self.config_id_to_tax_config.values()
                           if tc.tax_config_id in tax_config_ids and tc.service_category == tax_sku_dto.category_id]

            for price_dto in tax_sku_dto.prices:
                price_dto.tax_breakup = self.get_tax_breakup(price_dto=price_dto, attributes=attributes,
                                                             tax_configs=tax_configs,
                                                             taxable_amount_rule=taxable_amount_rule)
        return tax_sku_dtos

    def get_tax_breakup(self, price_dto: SkuPriceDTO, attributes, tax_configs: List[TaxConfigV5],
                        taxable_amount_rule=None):
        pretax_price = price_dto.taxable_amount if price_dto.has_pretax_price else None
        posttax_price = price_dto.posttax_price if price_dto.has_posttax_price else None
        tax_configs = [tax_config for tax_config in tax_configs if
                       tax_config.is_applicable(date=price_dto.date, pretax_price=pretax_price)]
        for tax_config in tax_configs:
            if not TaxApplicabilityService.is_tax_applicable(tax_config.tax_code, attributes):
                tax_config.tax_value = 0

        # Subtract `included_in_rate` tax component from `pretax_price`, to get actual pretax price
        # or subtract all tax component from `posttax_price`, to reverse compute tax
        if price_dto.has_pretax_price:
            if any(c for c in tax_configs if c.included_in_rate):
                percent_tax_value = sum(
                    [c.tax_value for c in tax_configs if c.tax_type == TaxType.PERCENTAGE and c.included_in_rate], 0)
                flat_tax_value = sum(
                    [c.tax_value for c in tax_configs if c.tax_type == TaxType.FLAT and c.included_in_rate], 0)

                pretax_price = price_dto.pretax_price - flat_tax_value
                pretax_price = (pretax_price * 100) / (percent_tax_value + 100)
                price_dto.pretax_price = pretax_price
            else:
                # For cases where price passed to tax service is inclusive of any tax, then we don't support
                # deduction of taxable amount using taxable_amount_rule. We'll just assume what is passed in the API
                if taxable_amount_rule:
                    # Taxable Amount rule will only apply when price sent to tax service is actual pretax price
                    price_dto.update_taxable_amount(taxable_amount_rule)
                pretax_price = price_dto.taxable_amount
        else:
            tax_configs = self.populate_posttax_ranges(tax_configs)
            tax_configs = [tax_config for tax_config in tax_configs if
                           tax_config.is_applicable(posttax_price=posttax_price)]
            percent_tax_value = sum([c.tax_value for c in tax_configs if c.tax_type == TaxType.PERCENTAGE], 0)
            flat_tax_value = sum([c.tax_value for c in tax_configs if c.tax_type == TaxType.FLAT], 0)

            pretax_price = price_dto.posttax_price - flat_tax_value
            pretax_price = (pretax_price * 100) / (percent_tax_value + 100)
            price_dto.pretax_price = pretax_price

        return [TaxBreakupDTO(tax_amount=config.evaluate_tax_from_pretax(pretax_price),
                              tax_type=config.tax_type, tax_code=config.tax_code, tax_value=round(config.tax_value, 2))
                for config in tax_configs]

    def populate_posttax_ranges(self, tax_configs: List[TaxConfigV5]):
        pretax_ranges = list({c.pretax_range for c in tax_configs})
        pretax_to_posttax_range_map = {}
        for rng in pretax_ranges:
            lower_bound_percent_value, upper_bound_percent_value = self.calculate_percentage_bounds_value(
                tax_configs, rng)
            lower_bound_flat_value, upper_bound_flat_value = self.calculate_flat_bounds_value(tax_configs, rng)
            pretax_to_posttax_range_map[rng] = Range(
                    lower=rng.lower * Decimal(1 + lower_bound_percent_value / 100) + lower_bound_flat_value,
                    higher=rng.higher * Decimal(1 + upper_bound_percent_value / 100) + upper_bound_flat_value)
        for config in tax_configs:
            config.posttax_range = pretax_to_posttax_range_map.get(config.pretax_range)
        return tax_configs

    def get_tax_configs_for_property(self, hotel_id):
        tax_configs = self.tax_config_repository.get_tax_configs_for_hotel(hotel_id)
        return tax_configs
