from taxation.domain.tax import repo_provider
from taxation.domain.tax.dto.booking_dto import BookingDto
from taxation.domain.tax.entities.tax_config import TaxConfig


class TaxService(object):
    def __init__(self):
        self.service_repository = repo_provider.service_repository
        self.hotel_config_repository = repo_provider.hotel_config_repository
        self.sku_service_name_mapping_repository = repo_provider.sku_service_name_mapping_repository
        self.gst_repository = repo_provider.gst_repository
        self.state_repository = repo_provider.state_repository

    def compute_tax(self, booking: BookingDto, reverse_tax=False):
        self.enrich_booking_dto(booking)
        state_ids = booking.get_state_ids()
        service_ids = booking.get_service_ids()

        cgst, sgst, other_tax = self.gst_repository.get_tax_config_for_given_state_and_service(
            service_ids, state_ids)

        sgst_list = [item for item in sgst if item.service_id in service_ids]
        other_tax_list = [item for item in other_tax if item.service_id in service_ids]

        tax_config = TaxConfig(cgst=cgst, sgst=sgst_list, igst=[], other_tax=other_tax_list)
        tax_config.calculate_tax(booking, reverse_tax=reverse_tax)

    def enrich_booking_dto(self, booking):
        hotel_ids = booking.get_hotel_ids()
        state_ids = booking.get_state_ids()
        sku_code_to_category_map = self.get_sku_code_to_category_map(booking.get_sku_codes())
        services = self.service_repository.get_services()
        booking.enrich(services, sku_code_to_category_map,
                       self.get_hotel_to_state_mapping(hotel_ids) if hotel_ids else None,
                       self.get_cs_state_id_to_state_mapping(state_ids) if state_ids else None)

    def get_cs_state_id_to_state_mapping(self, cs_state_ids):
        states = self.state_repository.fetch_states_by_cs_state_ids(cs_state_ids)
        return {state.cs_state_id: state.state_id for state in states}

    def get_hotel_to_state_mapping(self, hotel_ids):
        hotels = self.hotel_config_repository.get_hotel_configs_by_cs_id(hotel_ids)
        return {hotel.cs_hotel_id: hotel.state_id for hotel in hotels}

    def get_sku_code_to_category_map(self, sku_codes):
        result = dict()
        if not sku_codes:
            return result

        sku_category_codes = self.sku_service_name_mapping_repository.get_sku_category_code(sku_codes)
        for sku_code, sku_category_code in zip(sku_codes, sku_category_codes):
            result[sku_code] = sku_category_code
        return result
