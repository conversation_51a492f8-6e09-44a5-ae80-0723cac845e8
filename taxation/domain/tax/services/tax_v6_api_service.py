from decimal import Decimal
from typing import List

from taxation.api.v5.tax_sku_builder import TaxSkuBuilder

from taxation.domain.tax.dto.sku_attributes_dto import AttributeKey
from taxation.domain.tax.dto.sku_price_dto import SkuPriceDTO
from taxation.domain.tax.dto.tax_breakup_dto import TaxBreakupDTO
from taxation.domain.tax.entities.v5.tax_config import TaxConfigV5
from taxation.domain.tax.services.base_tax_service import BaseTaxService
from taxation.domain.tax.services.tax_applicability.tax_applicability_service import TaxApplicabilityService
from taxation.domain.tax.value_objects.range import Range
from taxation.models.tax_model import TaxType


class TaxV6ApiService(BaseTaxService):
    def __init__(self):
        super(TaxV6ApiService, self).__init__()

    def compute_tax(self, skus):
        tax_sku_dtos = [TaxSkuBuilder.build(sku) for sku in skus]
        hotel_ids = set()
        sku_category_ids = set()

        for tax_sku_dto in tax_sku_dtos:
            hotel_id = tax_sku_dto.attributes.get(AttributeKey.HOTEL_ID)
            if hotel_id:
                hotel_ids.add(hotel_id)
            sku_category_ids.add(tax_sku_dto.category_id)

        self.set_config_id_to_tax_config(sku_category_ids, tax_types=[TaxType.PERCENTAGE.value])
        hotel_id_to_tax_config_ids = self.get_hotel_id_to_tax_config_ids(hotel_ids)

        for tax_sku_dto in tax_sku_dtos:
            attributes = tax_sku_dto.attributes
            hotel_id = attributes.get(AttributeKey.HOTEL_ID)
            tax_config_ids = hotel_id_to_tax_config_ids.get(hotel_id, [])

            tax_configs = [tc for tc in self.config_id_to_tax_config.values()
                           if tc.tax_config_id in tax_config_ids and tc.service_category == tax_sku_dto.category_id]

            for price_dto in tax_sku_dto.prices:
                price_dto.tax_breakup = self.get_tax_breakup(price_dto=price_dto, attributes=attributes,
                                                             tax_configs=tax_configs)
        return tax_sku_dtos

    def get_tax_breakup(self, price_dto: SkuPriceDTO, attributes, tax_configs: List[TaxConfigV5]):
        pretax_price = price_dto.taxable_amount if price_dto.has_pretax_price else None
        posttax_price = price_dto.posttax_price if price_dto.has_posttax_price else None
        tax_configs = [tax_config for tax_config in tax_configs if
                       tax_config.is_applicable(date=price_dto.date, pretax_price=pretax_price)]
        for tax_config in tax_configs:
            if not TaxApplicabilityService.is_tax_applicable(tax_config.tax_code, attributes):
                tax_config.tax_value = 0

        if not price_dto.has_pretax_price:
            tax_configs = self.populate_posttax_ranges(tax_configs)
            tax_configs = [tax_config for tax_config in tax_configs if
                           tax_config.is_applicable(posttax_price=posttax_price)]
            percent_tax_value = sum([c.tax_value for c in tax_configs if c.tax_type == TaxType.PERCENTAGE], 0)
            pretax_price = (price_dto.posttax_price * 100) / (percent_tax_value + 100)
            price_dto.pretax_price = pretax_price

        return [TaxBreakupDTO(tax_amount=config.evaluate_tax_from_pretax(pretax_price),
                              tax_type=config.tax_type, tax_code=config.tax_code, tax_value=round(config.tax_value, 2))
                for config in tax_configs]

    def populate_posttax_ranges(self, tax_configs: List[TaxConfigV5]):
        pretax_ranges = list({c.pretax_range for c in tax_configs})
        pretax_to_posttax_range_map = {}
        for rng in pretax_ranges:
            lower_bound_percent_value, upper_bound_percent_value = self.calculate_percentage_bounds_value(
                tax_configs, rng)
            pretax_to_posttax_range_map[rng] = Range(
                lower=rng.lower * Decimal(1 + lower_bound_percent_value / 100),
                higher=rng.higher * Decimal(1 + upper_bound_percent_value / 100))
        for config in tax_configs:
            config.posttax_range = pretax_to_posttax_range_map.get(config.pretax_range)
        return tax_configs
