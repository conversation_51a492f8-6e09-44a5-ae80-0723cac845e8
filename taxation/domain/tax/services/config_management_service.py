import logging

import pytz

from taxation.alerts import slack_alert
from taxation.domain.catalog.cs_service import CatalogingService
from taxation.domain.tax import repo_provider as rp, repo_provider
from taxation.domain.tax.dto.seller_dto import SellerDto
from taxation.models import State, HotelConfig, RoomConfig, Service, HotelTaxConfigMapping
from taxation.models.seller import Seller
from taxation.settings import get_config_class

time_zone = pytz.timezone('Asia/Kolkata')

logger = logging.getLogger(__name__)

ROOM_NAME_LIST = get_config_class().ROOM_NAME_LIST


class ConfigManagementService:
    @classmethod
    def hotel_sync_from_web(cls, hotel_dto):
        hotel_id = hotel_dto.hotel_id

        hotel_by_hotel_id = rp.hotel_config_repository.fetch_by_hotel_id(hotel_id)
        hotel_by_cs_id = rp.hotel_config_repository.fetch_by_cs_hotel_id(
            hotel_dto.catalog_property_id) if hotel_dto.catalog_property_id else None
        if hotel_by_cs_id:
            if (not hotel_by_hotel_id) or (hotel_by_hotel_id.id == hotel_by_cs_id.id):
                cls.update_property_model_object_from_web(hotel_by_cs_id, hotel_dto)
                rp.hotel_config_repository.update()
                # update-returning existing data, False: updated data, no creation.
                return hotel_by_cs_id, True
            message = "Hotel sync failed, Either of Hx_id or web_hotel_id already associated with other hotel, " \
                      "data: %s" % hotel_dto.__dict__
            logger.error(message)
            slack_alert(message=message)
            # error scenario
            return None, False
        else:
            # do check for cs_hotel_id equality, if present/relevant
            if hotel_by_hotel_id and (
                    not hotel_by_hotel_id.cs_hotel_id or hotel_by_hotel_id.cs_hotel_id ==
                    hotel_dto.catalog_property_id):
                cls.update_property_model_object_from_web(hotel_by_hotel_id, hotel_dto)
                rp.hotel_config_repository.update()
                return hotel_by_hotel_id, True

        return None, True

    @staticmethod
    def update_property_model_object_from_web(hotel_object, hotel_dto):
        hotel_object.hx_id = hotel_dto.hx_id
        hotel_object.hotel_name = hotel_dto.hotel_name
        hotel_object.hotel_id = hotel_dto.hotel_id
        hotel_object.cs_hotel_id = hotel_dto.catalog_property_id

    @classmethod
    def hotel_sync_from_cs(cls, hotel_dto):
        cs_property_id = hotel_dto.cs_hotel_id.strip()
        churned = False if hotel_dto.status in ['LIVE'] else True
        """
        following possible status codes/values from CS(cataloging service):
        STATUS_NEAR_CONFIRMED = 'NEAR_CONFIRMED'
        STATUS_NOT_SIGNING = 'NOT_SIGNING'
        STATUS_SIGNED = 'SIGNED'
        STATUS_DROPPED = 'DROPPED_POST_SIGNING'
        STATUS_LIVE = 'LIVE'
        STATUS_CHURNED = 'CHURNED'
        """
        state_id = str(hotel_dto.state_id)
        state = rp.state_repository.fetch_state_by_cs_state_id(state_id)
        """
        # checking based on name as name is the common link between web and cs data sources, no other common field
        # state_by_state_name = rp.state_repository.fetch_by_state_name(hotel_dto.state_name)
        # if state_by_state_name:
        #     state_by_state_name.cs_state_id = state_id
        #     rp.state_repository.update()
        #     state = state_by_state_name
        # else:
        #     state = State(state_name=hotel_dto.state_name, cs_state_id=state_id)
        #     rp.state_repository.create(state)
        """
        if not state:
            state = State(state_name=hotel_dto.state_name, cs_state_id=state_id)
            rp.state_repository.create(state)
        hotel_by_cs_id = rp.hotel_config_repository.fetch_by_cs_hotel_id(cs_property_id)

        # hx_id, cs_property_id, state_id, state_name, status, name, hotel_id=None
        if hotel_by_cs_id:
            hotel_by_cs_id.cs_hotel_id = cs_property_id
            hotel_by_cs_id.hotel_name = hotel_dto.hotel_name
            hotel_by_cs_id.churned = churned
            hotel_by_cs_id.hx_id = hotel_dto.hx_id
            hotel_by_cs_id.state_identifier = state.id
            hotel_by_cs_id.state_id = state.state_id
            rp.hotel_config_repository.update()
            return cls.room_config_sync_using_property(hotel_dto.property_room_dtos)
        else:
            hotel_config = HotelConfig(state_identifier=state.id, hotel_name=hotel_dto.hotel_name,
                                       hx_id=hotel_dto.hx_id, cs_hotel_id=cs_property_id,
                                       churned=churned, state_id=state.state_id)
            rp.hotel_config_repository.create(hotel_config)
            return cls.room_config_sync_using_property(hotel_dto.property_room_dtos)

    @classmethod
    def activate_inactive_sku(cls, ids):
        hotel_configs = repo_provider.hotel_config_repository.fetch_all(ids)
        cs_service = CatalogingService()
        property_to_sku_map = {hotel_config.cs_hotel_id: cs_service.get_property_skus(hotel_config.cs_hotel_id)
                               for hotel_config in hotel_configs}
        for hotel_config in hotel_configs:
            inactive_skus = []
            invalid_skus = []
            skus = property_to_sku_map.get(hotel_config.cs_hotel_id)
            for sku in skus:
                sku_code = sku['code']
                inactive_skus.append(sku_code)
                sku_category_list = repo_provider.sku_mapping_repository.get_category_list_from_sku_code(sku_code)
                for sku_category in sku_category_list:
                    if not rp.service_repository.fetch_by_category_id(sku_category):
                        invalid_skus.append(sku_code)
                        break
            # remove invalid skus from set of all inactive skus
            skus_to_activate = list(set(inactive_skus) - set(invalid_skus))
            cs_service.activate_sku_on_cataloging_service(hotel_config.cs_hotel_id, skus_to_activate)

    @staticmethod
    def room_config_sync_using_property(property_room_dtos):
        for property_room_dto in property_room_dtos:
            room_type_name = property_room_dto.name.lower()
            if room_type_name not in ROOM_NAME_LIST:
                message = "Invalid room name in the data: %s" % property_room_dto
                logger.error("Error: %s, Data: %s" % (message, property_room_dto))
                slack_alert(message=message, data=property_room_dto)
                return False
            room_config = rp.room_config_repository.fetch_by_cs_hotel_id_and_room_code(property_room_dto.cs_hotel_id,
                                                                                       property_room_dto.room_code)
            if room_config:
                room_config.name = room_type_name
                rp.room_config_repository.update()
            else:
                room_config = RoomConfig(cs_hotel_id=property_room_dto.cs_hotel_id, name=room_type_name,
                                         room_code=property_room_dto.room_code)
                rp.room_config_repository.create(room_config)
        return True

    @staticmethod
    def room_sync_from_cs(room_dto):
        # cs_hotel_id, room_type, room_code
        name = room_dto.name
        cs_property_id = room_dto.cs_hotel_id
        room_code = room_dto.room_code

        if name.lower() not in ROOM_NAME_LIST:
            message = "Invalid room name in the data: %s, cs_property_id: %s" % (name, cs_property_id)
            logger.error("Error: %s, Data: %s" % (message, room_dto))
            slack_alert(message=message, data=room_dto.__dict__)
            return False

        room_config = rp.room_config_repository.fetch_by_cs_hotel_id_and_room_code(cs_property_id, room_code)

        if room_config:
            if room_config.name != name:
                room_config.name = name
                rp.room_config_repository.update()
            return True
        else:
            hotel = rp.hotel_config_repository.fetch_by_cs_hotel_id(cs_property_id)
            if not hotel:
                message = "Hotel doesn't exist, cs_hotel_id:%s" % cs_property_id
                logger.error("Error: %s, Data: %s" % (message, str(room_dto)))
                slack_alert(message=message, data=room_dto.__dict__)
                return False
            room_config = RoomConfig(cs_hotel_id=cs_property_id, name=name, room_code=room_code)
            rp.room_config_repository.create(room_config)
            return True

    @classmethod
    def service_sync_from_cs(cls, sku_category_dto):
        tax_service = rp.service_repository.fetch_by_category_id(sku_category_dto.code)

        if tax_service:
            cls.update_service_model_object(tax_service, sku_category_dto)
            rp.service_repository.update()
        else:
            tax_service = Service(
                name=sku_category_dto.name,
                service_accounting_code=sku_category_dto.service_accounting_code,
                category_id=sku_category_dto.code,
            )
            rp.service_repository.create(tax_service)
            logger.info("New Service(category) added with id: %s", tax_service.id)

        return True

    @staticmethod
    def update_service_model_object(tax_service, sku_category_dto):
        tax_service.name = sku_category_dto.name
        tax_service.service_accounting_code = sku_category_dto.service_accounting_code
        tax_service.category_id = sku_category_dto.code

    @classmethod
    def seller_sync_from_cs(cls, seller_dto: SellerDto):
        existing_seller = rp.seller_repository.fetch_by_seller_id(seller_dto.seller_id)

        state_id = str(seller_dto.state_id)
        state = rp.state_repository.fetch_state_by_cs_state_id(state_id)

        # hx_id, cs_property_id, state_id, state_name, status, name, hotel_id=None
        if existing_seller:
            existing_seller.name = seller_dto.name
            rp.seller_repository.update(existing_seller)
            return existing_seller
        else:
            seller = Seller(seller_id=seller_dto.seller_id, name=seller_dto.name,
                            category=seller_dto.seller_category_id, state_id=state.state_id)
            rp.seller_repository.create(seller)
            return seller

    @classmethod
    def create_tax_config_as_property_onboards(self, hotel_id, state_id):
        cs_service = CatalogingService()
        is_onboarding_tax_enabled_via_consumer = cs_service.is_onboarding_tax_enabled_via_consumer()
        if not is_onboarding_tax_enabled_via_consumer:
            return
        tax_configs = repo_provider.tax_config_repository.get_all_tax_configs_by_state_id(state_id)
        map_tax_config = [HotelTaxConfigMapping(hotel_id=hotel_id, tax_config_id=tax_config.tax_config_id) for
                          tax_config in tax_configs]
        repo_provider.hotel_config_repository.create_all(map_tax_config)
