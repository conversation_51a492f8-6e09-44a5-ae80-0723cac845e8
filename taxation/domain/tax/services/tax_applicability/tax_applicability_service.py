from taxation.domain.tax.services.tax_applicability.rules.cgst import CgstRule
from taxation.domain.tax.services.tax_applicability.rules.igst import IgstRule
from taxation.domain.tax.services.tax_applicability.rules.kerala_flood_cess import KeralaFloodCessRule
from taxation.domain.tax.services.tax_applicability.rules.sgst import SgstRule


class TaxApplicabilityService:
    tax_code_rule_map = dict(sgst=SgstRule,
                             cgst=CgstRule,
                             igst=IgstRule,
                             kerala_flood_cess=KeralaFloodCessRule)

    @staticmethod
    def is_tax_applicable(tax_code, attributes):
        rule = TaxApplicabilityService.tax_code_rule_map.get(tax_code)
        if not rule:
            return True
        return rule().is_applicable(attributes)
