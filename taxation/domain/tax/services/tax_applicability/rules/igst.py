from taxation.domain.tax.constants import AttributeKey
from taxation.domain.tax.services.tax_applicability.rules.base_rule import BaseRule


class IgstRule(BaseRule):

    def is_applicable(self, attributes):
        if not attributes.get(AttributeKey.IS_BUYER_IN_SEZ):
            return False
        if attributes.get(AttributeKey.BUYER_HAS_LUT) and attributes.get(AttributeKey.SELLER_HAS_LUT):
            return False
        return True
