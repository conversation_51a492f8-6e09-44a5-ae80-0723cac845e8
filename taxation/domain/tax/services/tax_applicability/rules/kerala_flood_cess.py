from taxation.domain.tax.constants import AttributeKey
from taxation.domain.tax.services.tax_applicability.rules.base_rule import BaseRule
from taxation.utils.stateful_utils import is_kerala_gstin


class KeralaFloodCessRule(BaseRule):

    def is_applicable(self, attributes):
        gstin = attributes.get(AttributeKey.BUYER_GSTIN)
        if is_kerala_gstin(gstin):
            return False
        return True
