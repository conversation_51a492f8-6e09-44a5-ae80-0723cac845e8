import logging

from taxation.domain.tax.entities.tax_config import TaxConfig
from taxation.domain.tax.entities.tax_config_v4 import TaxConfigV4
from taxation.infrastructure.cs_client import CatalogingServiceApiClient

logger = logging.getLogger(__name__)
KEY_PREFIX = 'taxation_'


class TaxV4ApiService(object):
    def __init__(self, repo_provider):
        self.CACHE_KEY = 'sku_code_service_name_list'
        self.CACHE_KEY_BY_CS_ID = 'hotel_configs_cs_id'
        self.hotel_config_repository = repo_provider.hotel_config_repository
        self.service_repository = repo_provider.service_repository
        self.gst_repository = repo_provider.gst_repository
        self.sku_service_name_mapping_repository = repo_provider.sku_service_name_mapping_repository
        self.cs_client = CatalogingServiceApiClient()

    def calculate_tax(self, parsed_request):
        # Load TaxConfigurations from repositories
        hotel_list, sku_codes = self.get_unique_hotel_and_sku(parsed_request)

        hotel_state_dict, state_ids, sku_code_to_service_map, service_ids = \
            self._get_state_and_service_ids(hotel_list, sku_codes)

        cgst, sgst, other_tax = self.gst_repository.get_tax_config_for_given_state_and_service(
            service_ids, state_ids)

        sgst_list = [item for item in sgst if item.service_id in service_ids]
        other_tax_list = [item for item in other_tax if item.service_id in service_ids]

        tax_config = TaxConfigV4(cgst=cgst, sgst=sgst_list, igst=[], other_tax=other_tax_list)
        response_data = tax_config.calculate_tax(parsed_request, hotel_state_dict, sku_code_to_service_map)
        return response_data

    @staticmethod
    def get_unique_hotel_and_sku(parsed_request):
        hotel_list = []
        sku_codes = []
        for sku in parsed_request['skus']:
            for price in sku['prices']:
                for sku_code in price['sku_prices']:
                    if sku_code not in sku_codes:
                        sku_codes.append(sku_code['sku_code'])
            for attr in sku['attributes']:
                if attr['key'] == 'hotel_id':
                    if attr['value'] not in hotel_list:
                        hotel_list.append(attr['value'])
        return hotel_list, sku_codes

    def _get_state_and_service_ids(self, hotel_ids, sku_codes):
        hotel_state_dict, state_ids = self.get_hotel_to_state_mapping(hotel_ids)
        services = self.service_repository.get_services()

        sku_code_to_service_map, service_ids = self.get_sku_code_to_service_map(sku_codes, services)
        return hotel_state_dict, state_ids, sku_code_to_service_map, service_ids

    def get_hotel_to_state_mapping(self, hotel_ids):
        hotels = self.hotel_config_repository.get_hotel_configs_by_cs_id(hotel_ids)
        hotel_state_dict = dict()
        state_ids = set()
        for hotel in hotels:
            hotel_state_dict[hotel.cs_hotel_id] = hotel.state_id
            state_ids.add(hotel.state_id)
        return hotel_state_dict, state_ids

    def get_sku_code_to_service_map(self, sku_codes, services):
        sku_category_codes = self.sku_service_name_mapping_repository.get_sku_category_code(sku_codes)
        category_id_to_service_map = {service.category_id: service for service in services}
        sku_code_to_service_map = dict()
        service_ids = set()
        for sku_code, sku_category_code in zip(sku_codes, sku_category_codes):
            service = category_id_to_service_map.get(sku_category_code)
            sku_code_to_service_map[sku_code] = service
            service_ids.add(service.id)
        return sku_code_to_service_map, service_ids
