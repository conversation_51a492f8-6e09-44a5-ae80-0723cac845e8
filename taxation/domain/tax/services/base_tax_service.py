from taxation.domain.tax import repo_provider
from taxation.models.tax_model import TaxType


class BaseTaxService:
    def __init__(self):
        self.tax_config_repository = repo_provider.tax_config_repository
        self.config_id_to_tax_config = dict()
        self.taxable_amount_rules = list()

    def set_config_id_to_tax_config(self, sku_category_ids, tax_types=None):
        """
        Set the tax configs for the given category ids and tax types. if no tax types are provided, all tax types are
        considered
        :param sku_category_ids: Set of category ids
        :param tax_types: List of tax types
        """
        self.config_id_to_tax_config = self.tax_config_repository.get_tax_configs(sku_category_ids, tax_types)

    def get_hotel_id_to_tax_config_ids(self, hotel_ids):
        hotel_id_to_tax_config_ids = dict()
        if hotel_ids:
            hotel_id_to_tax_config_ids = self.tax_config_repository.get_tax_config_mapping_for_hotels(
                hotel_ids, self.config_id_to_tax_config.keys())
        return hotel_id_to_tax_config_ids

    def seller_id_to_tax_config_ids(self, seller_ids):
        seller_id_to_tax_config_ids = dict()
        if seller_ids:
            seller_id_to_tax_config_ids = self.tax_config_repository.get_tax_config_mapping_for_sellers(
                seller_ids, self.config_id_to_tax_config.keys())
        return seller_id_to_tax_config_ids

    def hotel_to_taxable_amount_rule_ids(self, hotel_ids, sku_category_ids):
        self.taxable_amount_rules = self.tax_config_repository.get_taxable_amount_rules(sku_category_ids)
        rule_id_to_taxable_amount = {ta.rule_id: ta for ta in self.taxable_amount_rules}

        hotel_to_taxable_amount_rule_ids = dict()
        if rule_id_to_taxable_amount:
            hotel_to_taxable_amount_rule_ids = self.tax_config_repository.get_hotel_to_rule_id_mapping(hotel_ids)
        return hotel_to_taxable_amount_rule_ids

    @staticmethod
    def calculate_percentage_bounds_value(tax_configs, rng):
        lower_bound_value = 0
        higher_bound_value = 0
        for c in tax_configs:
            if c.tax_type == TaxType.PERCENTAGE:
                if c.pretax_range.contains(rng.lower):
                    lower_bound_value += c.tax_value
                if c.pretax_range.contains(rng.higher):
                    higher_bound_value += c.tax_value
        return lower_bound_value, higher_bound_value

    @staticmethod
    def calculate_flat_bounds_value(tax_configs, rng):
        lower_bound_value = 0
        higher_bound_value = 0
        for c in tax_configs:
            if c.tax_type == TaxType.FLAT:
                if c.pretax_range.contains(rng.lower):
                    lower_bound_value += c.tax_value
                if c.pretax_range.contains(rng.higher):
                    higher_bound_value += c.tax_value
        return lower_bound_value, higher_bound_value

    def compute_tax(self, skus):
        raise NotImplementedError
