from taxation.infrastructure.base_repository import BaseRepository
from taxation.models import GlobalConfig


class GlobalConfigRepository(BaseRepository):
    def fetch_all(self):
        return self.session().query(GlobalConfig).all()

    def fetch_one(self, config_name=None):
        if config_name is None:
            return None
        return self.session().query(GlobalConfig).filter(GlobalConfig.config_name == config_name).one_or_none()
