import logging
from ast import literal_eval

from taxation.domain.catalog.cs_service import CatalogingService
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseCachingRepository

logger = logging.getLogger(__name__)


class SkuMappingRepository(BaseCachingRepository):
    CACHE_KEY = 'sku_category_list'

    def get_category_list_from_sku_code(self, sku_code):
        pipe = cache.get_cache().pipeline(transaction=False)
        pipe.hmget(self.build_cache_key(self.CACHE_KEY), sku_code)
        results = pipe.execute()
        result = results[0][0]
        if result is not None:
            result = literal_eval(result)
            return result
        else:
            result = self.refresh_cache(sku_code)
            return result

    def refresh_cache(self, sku_code=None):
        try:
            cs_service = CatalogingService()
            sku_code_category_list_map_all = cs_service.get_sku_code_category_map_for_all_sku()

            if sku_code_category_list_map_all:
                cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY), sku_code_category_list_map_all)

        except Exception:
            logger.exception("Exception occurred in refresh_cache")

        if sku_code:
            return sku_code_category_list_map_all[sku_code]

        return sku_code_category_list_map_all
