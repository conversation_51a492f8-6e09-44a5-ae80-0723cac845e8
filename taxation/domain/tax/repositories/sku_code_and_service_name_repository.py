import logging
from taxation.domain.catalog.cs_service import CatalogingService
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseCachingRepository

logger = logging.getLogger(__name__)


class SkuCodeAndServiceNameRepository(BaseCachingRepository):
    CACHE_KEY = 'sku_code_service_name_list'

    def get_sku_category_code(self, sku_codes):
        pipe = cache.get_cache().pipeline(transaction=False)
        pipe.hmget(self.build_cache_key(self.CACHE_KEY), sku_codes)
        results = pipe.execute()[0]
        if all(r is not None for r in results):
            return results
        else:
            results = self.refresh_cache(sku_codes=sku_codes)
            return results

    def refresh_cache(self, sku_codes=None):
        try:
            sku_code_map = dict()
            sku_data = CatalogingService().get_all_sku_from_cataloging_service()
            sku_category_codes = []
            for data in sku_data:
                if not sku_codes or data['code'] in sku_codes:
                    sku_category_code = data['sku_category_code']
                    sku_category_codes.append(sku_category_code)
                    sku_code_map[data['code']] = sku_category_code

            cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY), sku_code_map)
            return sku_category_codes
        except Exception:
            logger.exception("Exception occurred in refresh_cache")
            return None
