import json
from ast import literal_eval
from collections import defaultdict

from sqlalchemy import event

from taxation.constants import OTHER_TAX_SUPPORTED_STATE_IDS
from taxation.domain.tax.repositories import strip_sa_instance_state
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseRepository
from taxation.jsonutils import DateTimeEncoder, date_hook
from taxation.models import CGST, SGST, OtherTax


class GSTRepository(BaseRepository):
    CGST_CACHE_KEY_IDENTIFIER = 'cgst_id'
    SGST_CACHE_KEY = 'sgst'
    SGST_CACHE_KEY_IDENTIFIER = 'sgst_id'
    OTHER_TAX_KEY = 'other_tax_key'

    def __init__(self, service_repository):
        self.service_repository = service_repository

    @classmethod
    def remove_from_cache(cls, model=None):
        if not model or model == "cgst":
            cache.get_cache().delete(cls.build_cache_key(cls.CGST_CACHE_KEY_IDENTIFIER))
        if not model or model == "sgst":
            cache.get_cache().delete(cls.build_cache_key(cls.SGST_CACHE_KEY))
        if not model or model == "other_tax":
            cache.get_cache().delete(cls.build_cache_key(cls.OTHER_TAX_KEY))

    def refresh_cgst_cache(self):
        # Refresh CGST in Cache
        results = self.session().query(CGST).all()
        cache_data = strip_sa_instance_state(results, 'service')
        hmset_data_by_id = defaultdict(list)
        for st in cache_data:
            hmset_data_by_id[st['service_id']].append(json.dumps(st, cls=DateTimeEncoder))

        if hmset_data_by_id:
            cache.get_cache().hmset(self.build_cache_key(self.CGST_CACHE_KEY_IDENTIFIER), hmset_data_by_id)
        return results

    def refresh_sgst_cache(self, state_ids=None):
        # Refresh SGST in Cache
        if state_ids:
            results = self.session().query(SGST).filter(SGST.state_id.in_(state_ids)).all()
        else:
            results = self.session().query(SGST).all()
        cache_data = strip_sa_instance_state(results, 'service', 'state')
        hmset_data = defaultdict(list)
        for st in cache_data:
            hmset_data[st['state_id']].append(json.dumps(st, cls=DateTimeEncoder))

        if hmset_data:
            cache.get_cache().hmset(self.build_cache_key(self.SGST_CACHE_KEY), hmset_data)
        return results

    def refresh_other_tax_cache(self, state_ids=None):
        if state_ids:
            results = self.session().query(OtherTax).filter(OtherTax.state_id.in_(state_ids)).all()
        else:
            results = self.session().query(OtherTax).all()
        cache_data = strip_sa_instance_state(results, 'service', 'state', 'tax_type')
        hmset_data = defaultdict(list)
        for st in cache_data:
            hmset_data[st['state_id']].append(json.dumps(st, cls=DateTimeEncoder))
        if hmset_data:
            cache.get_cache().hmset(self.build_cache_key(self.OTHER_TAX_KEY), hmset_data)
        return results

    def refresh_cache(self, model=None):
        if not model or model == "cgst":
            self.refresh_cgst_cache()
        if not model or model == "sgst":
            self.refresh_sgst_cache()
        if not model or model == "other_tax":
            self.refresh_other_tax_cache()

    def get_cgst(self, results):
        if all(r is not None for r in results):
            results = [CGST(**json.loads(x, object_hook=date_hook)) for d in results for x in literal_eval(d) if
                       x is not None]
        else:
            results = self.refresh_cgst_cache()
        return results

    def get_sgst_by_state_ids(self, results, state_ids):
        if all(r is not None for r in results):
            results = [SGST(**json.loads(x, object_hook=date_hook)) for d in results for x in literal_eval(d) if
                       x is not None]
        else:
            results = self.refresh_sgst_cache(state_ids=state_ids)
        return results

    def get_other_tax_by_state_ids(self, results, state_ids):
        if all(r is not None for r in results):
            results = [OtherTax(**json.loads(x, object_hook=date_hook)) for d in results for x in literal_eval(d) if
                       x is not None]
        else:
            results = self.refresh_other_tax_cache(state_ids=state_ids)
        return results

    def get_cgst_for_service(self, service_name):
        results = self.session().query(CGST).filter(CGST.service_name == service_name).all()
        return results

    def get_sgst_for_service_and_state(self, service_name, state_id):
        results = self.session().query(SGST).filter(SGST.service_name == service_name).filter(
            SGST.state_id == state_id).all()
        return results

    def get_tax_config_for_given_state_and_service(self, service_ids, state_ids):
        pipe = cache.get_cache().pipeline(transaction=False)
        pipe.hmget(self.build_cache_key(self.CGST_CACHE_KEY_IDENTIFIER), service_ids)
        pipe.hmget(self.build_cache_key(self.SGST_CACHE_KEY), state_ids)

        other_tax_supported_state_ids = [state_id for state_id in state_ids if state_id in
                                         OTHER_TAX_SUPPORTED_STATE_IDS]
        if other_tax_supported_state_ids:
            pipe.hmget(self.build_cache_key(self.OTHER_TAX_KEY), other_tax_supported_state_ids)
        cache_results = pipe.execute()

        cgst = self.get_cgst(cache_results[0])
        sgst = self.get_sgst_by_state_ids(cache_results[1], state_ids)
        other_tax = []
        if other_tax_supported_state_ids:
            other_tax = self.get_other_tax_by_state_ids(cache_results[2], state_ids)
        return cgst, sgst, other_tax


@event.listens_for(CGST, 'after_delete')
@event.listens_for(CGST, 'after_insert')
@event.listens_for(CGST, 'after_update')
def cgst_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    GSTRepository.remove_from_cache(model="cgst")
    repo_provider.gst_repository.refresh_cache(model="cgst")


@event.listens_for(OtherTax, 'after_delete')
@event.listens_for(OtherTax, 'after_insert')
@event.listens_for(OtherTax, 'after_update')
def other_tax_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    GSTRepository.remove_from_cache(model="other_tax")
    repo_provider.gst_repository.refresh_cache(model="other_tax")


@event.listens_for(SGST, 'after_delete')
@event.listens_for(SGST, 'after_insert')
@event.listens_for(SGST, 'after_update')
def sgst_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    GSTRepository.remove_from_cache(model="sgst")
    repo_provider.gst_repository.refresh_cache(model="sgst")
