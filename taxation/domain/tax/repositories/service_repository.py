import json

from sqlalchemy import func, event
from sqlalchemy.orm.exc import MultipleResultsFound

from taxation.domain.tax.repositories import strip_sa_instance_state
from taxation.exceptions import MultipleResultsFound as TaxMultipleResultsFound
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseRepository
from taxation.models import Service


class ServiceRepository(BaseRepository):
    CACHE_KEY_SERVICE_ID = 'services_id'
    CACHE_KEY_SERVICE_CODE = 'service_codes'
    _model = Service

    @classmethod
    def remove_from_cache(cls):
        cache.get_cache().delete(cls.build_cache_key(cls.CACHE_KEY_SERVICE_ID))
        cache.get_cache().delete(cls.build_cache_key(cls.CACHE_KEY_SERVICE_CODE))

    def refresh_cache(self, category_ids=None):
        query = self.session().query(Service)
        if category_ids:
            query = query.filter(Service.category_id.in_(category_ids))
        results = query.all()
        cache_data = strip_sa_instance_state(results)
        hmset_data = {service['id']: json.dumps(service) for service in cache_data}
        hmset_data_by_service_code = {service['category_id']: json.dumps(service) for service in cache_data}
        if hmset_data:
            cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY_SERVICE_ID), hmset_data)
        if hmset_data_by_service_code:
            cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY_SERVICE_CODE), hmset_data_by_service_code)
        return results

    def get_services(self, service_codes=None):
        pipe = cache.get_cache().pipeline(transaction=False)
        pipe.hmget(self.build_cache_key(self.CACHE_KEY_SERVICE_CODE), service_codes)
        results = pipe.execute()[0]
        if all(r is not None for r in results):
            results = [Service(**json.loads(d)) for d in results if d is not None]
        else:
            results = self.refresh_cache(category_ids=service_codes)
        return results

    def fetch_by_category_id(self, category_id):
        try:
            return self.session().query(Service).filter(Service.category_id == category_id).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound(
                "Multiple records found corresponding to service(sku category): %s" % category_id)

    def fetch_by_sac(self, sac):
        try:
            return self.session().query(Service).filter(Service.service_accounting_code == sac).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound(
                "Multiple entries found corresponding to service_accounting_code: %s in service(sku category)" % sac)

    def fetch_by_service_name(self, service_name=None, case_insensitive=False):
        # supposed to be unique as a text, depending upon filter, make respective query
        try:
            if case_insensitive:
                service = self.session().query(Service).filter(
                    func.lower(Service.name) == func.lower(service_name)).one_or_none()
            else:
                service = self.session().query(Service).filter(Service.name == service_name).one_or_none()
            return service
        except MultipleResultsFound:
            raise TaxMultipleResultsFound(
                "Multiple entries found corresponding to service_name: %s in service(sku category)" % service_name)


@event.listens_for(Service, 'after_delete')
@event.listens_for(Service, 'after_insert')
@event.listens_for(Service, 'after_update')
def hotel_config_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    ServiceRepository.remove_from_cache()
    repo_provider.service_repository.refresh_cache()
