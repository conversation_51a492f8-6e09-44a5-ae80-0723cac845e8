import json

from sqlalchemy import event
from sqlalchemy.orm.exc import MultipleResultsFound

from taxation.domain.tax.repositories import strip_sa_instance_state
from taxation.exceptions import MultipleResultsFound as TaxMultipleResultsFound
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseRepository
from taxation.models.seller import Seller


class SellerRepository(BaseRepository):
    CACHE_KEY = 'sellers'
    CACHE_KEY_BY_CS_ID = 'seller_by_id'
    _model = Seller

    @classmethod
    def remove_from_cache(cls):
        cache.get_cache().delete(cls.build_cache_key(cls.CACHE_KEY))

    def refresh_cache(self, seller_ids=None):
        if seller_ids:
            results = self.session().query(Seller).filter(Seller.seller_id.in_(seller_ids)).all()
        else:
            results = self.session().query(Seller).all()
        cache_data = strip_sa_instance_state(results, 'state')
        hmset_data = {seller['seller_id']: json.dumps(seller) for seller in cache_data}
        hmset_data_by_cs_id = {seller['seller_id']: json.dumps(seller) for seller in cache_data if
                               seller['seller_id'] is not None}
        if hmset_data:
            cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY), hmset_data)
        if hmset_data_by_cs_id:
            cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY_BY_CS_ID), hmset_data_by_cs_id)
        return results

    def fetch_by_seller_id(self, seller_id):
        try:
            return self.session().query(Seller).filter(Seller.seller_id == seller_id).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound("Multiple records found corresponding to hotel_id(web): %s" % seller_id)

    def fetch_all(self):
        return self.session().query(Seller).all()


@event.listens_for(Seller, 'after_delete')
@event.listens_for(Seller, 'after_insert')
@event.listens_for(Seller, 'after_update')
def seller_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    repo_provider.seller_repository.remove_from_cache()
    repo_provider.seller_repository.refresh_cache()
