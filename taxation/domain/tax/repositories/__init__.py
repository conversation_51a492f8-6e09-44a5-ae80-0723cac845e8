import copy
import logging

logger = logging.getLogger(__name__)


def strip_sa_instance_state(results, *foreign_key_rel):
    logger.debug("Stripping _sa_instance_state")
    cache_data = []
    for r in results:
        d = copy.copy(r.__dict__)
        for f in foreign_key_rel:
            if f in d:
                d.pop(f)
        d.pop('_sa_instance_state')
        cache_data.append(d)
    return cache_data
