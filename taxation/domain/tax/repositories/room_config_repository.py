import json
import logging
from ast import literal_eval
from collections import defaultdict

from sqlalchemy import func, event
from sqlalchemy.orm.exc import MultipleResultsFound

from taxation.domain.tax.repositories import strip_sa_instance_state
from taxation.exceptions import MultipleResultsFound as TaxMultipleResultsFound
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseRepository
from taxation.jsonutils import date_hook, DateTimeEncoder
from taxation.models import RoomConfig

logger = logging.getLogger(__name__)


class RoomConfigRepository(BaseRepository):
    _model = RoomConfig
    CACHE_KEY = 'room_config'

    def fetch_by_cs_hotel_id_and_room_code(self, cs_hotel_id, room_code):
        try:
            return self.session().query(RoomConfig).filter(RoomConfig.cs_hotel_id == cs_hotel_id).filter(
                RoomConfig.room_code == room_code).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound(
                "Multiple records found corresponding to hotel_id(catalog), room_code(catalog): (%s, %s)" % (
                    cs_hotel_id, room_code))

    @classmethod
    def remove_from_cache(cls):
        cache.get_cache().delete(cls.build_cache_key(cls.CACHE_KEY))

    def get_all_room_configs(self, hotel_ids):
        pipe = cache.get_cache().pipeline(transaction=False)
        pipe.hmget(self.build_cache_key(self.CACHE_KEY), hotel_ids)
        results = pipe.execute()[0]
        if all(r is not None for r in results):
            results = [RoomConfig(**json.loads(x, object_hook=date_hook)) for d in results for x in literal_eval(d) if
                       x is not None]
        else:
            results = self.refresh_cache(hotel_ids=hotel_ids)
        return results

    def refresh_cache(self, hotel_ids=None):
        results = []
        try:
            if hotel_ids:
                results = self.session().query(RoomConfig).filter(RoomConfig.cs_hotel_id.in_(hotel_ids)).all()
            else:
                results = self.session().query(RoomConfig).all()
            cache_data = strip_sa_instance_state(results)
            hmset_data = defaultdict(list)
            for st in cache_data:
                hmset_data[st['cs_hotel_id']].append(json.dumps(st, cls=DateTimeEncoder))

            if hmset_data:
                cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY), hmset_data)
        except Exception:
            logger.exception("Exception occurred in refresh_cache")
        return results


@event.listens_for(RoomConfig, 'after_delete')
@event.listens_for(RoomConfig, 'after_insert')
@event.listens_for(RoomConfig, 'after_update')
def hotel_config_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    RoomConfigRepository.remove_from_cache()
    repo_provider.room_config_repository.refresh_cache()
