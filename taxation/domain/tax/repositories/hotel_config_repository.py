import json

from sqlalchemy import event
from sqlalchemy.orm.exc import MultipleResultsFound

from taxation.domain.tax.repositories import strip_sa_instance_state
from taxation.exceptions import MultipleResultsFound as TaxMultipleResultsFound
from taxation.infrastructure import cache
from taxation.infrastructure.base_repository import BaseRepository
from taxation.models import HotelConfig


class HotelConfigRepository(BaseRepository):
    CACHE_KEY = 'all_hotel_configs'
    NEW_CACHE_KEY = 'hotel_configs'
    CACHE_KEY_BY_CS_ID = 'hotel_configs_cs_id'
    _model = HotelConfig

    @classmethod
    def remove_from_cache(cls):
        cache.get_cache().delete(cls.build_cache_key(cls.CACHE_KEY))
        cache.get_cache().delete(cls.build_cache_key(cls.NEW_CACHE_KEY))

    def refresh_cache(self, hotel_ids=None, cs_hotel_ids=None):
        if hotel_ids:
            results = self.session().query(HotelConfig).filter(HotelConfig.hotel_id.in_(hotel_ids)).all()
        elif cs_hotel_ids:
            results = self.session().query(HotelConfig).filter(HotelConfig.cs_hotel_id.in_(cs_hotel_ids)).all()
        else:
            results = self.session().query(HotelConfig).all()
        cache_data = strip_sa_instance_state(results, 'state')
        hmset_data = {hotel['hotel_id']: json.dumps(hotel) for hotel in cache_data}
        hmset_data_by_cs_id = {hotel['cs_hotel_id']: json.dumps(hotel) for hotel in cache_data if
                               hotel['cs_hotel_id'] is not None}
        if hmset_data:
            cache.get_cache().hmset(self.build_cache_key(self.NEW_CACHE_KEY), hmset_data)
        if hmset_data_by_cs_id:
            cache.get_cache().hmset(self.build_cache_key(self.CACHE_KEY_BY_CS_ID), hmset_data_by_cs_id)
        return results

    def get_hotel_configs_by_cs_id(self, cs_hotel_ids):
        pipe = cache.get_cache().pipeline(transaction=False)
        pipe.hmget(self.build_cache_key(self.CACHE_KEY_BY_CS_ID), cs_hotel_ids)
        results = pipe.execute()[0]
        if all(r is not None for r in results):
            results = [HotelConfig(**json.loads(d)) for d in results if d is not None]
        else:
            results = self.refresh_cache(cs_hotel_ids=cs_hotel_ids)
        return results

    def fetch_by_hotel_id(self, hotel_id):
        try:
            return self.session().query(HotelConfig).filter(HotelConfig.hotel_id == hotel_id).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound("Multiple records found corresponding to hotel_id(web): %s" % hotel_id)

    def fetch_by_cs_hotel_id(self, cs_hotel_id):
        try:
            return self.session().query(HotelConfig).filter(HotelConfig.cs_hotel_id == cs_hotel_id).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound("Multiple records found corresponding to hotel_id(catalog): %s" % cs_hotel_id)

    def fetch_all(self, ids=None):
        if ids:
            return self.session().query(HotelConfig).filter(HotelConfig.id.in_(ids)).all()
        return self.session().query(HotelConfig).all()


@event.listens_for(HotelConfig, 'after_delete')
@event.listens_for(HotelConfig, 'after_insert')
@event.listens_for(HotelConfig, 'after_update')
def hotel_config_change_events(mapper, connection, target):
    from taxation.domain.tax import repo_provider
    HotelConfigRepository.remove_from_cache()
    repo_provider.hotel_config_repository.refresh_cache()
