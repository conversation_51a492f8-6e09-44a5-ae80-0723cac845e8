from collections import defaultdict
from typing import List, Dict

from sqlalchemy import or_
from sqlalchemy.orm import joinedload

from taxation import constants
from taxation.domain.tax.entities.v5.tax_config import TaxConfigV5, TaxableAmountRuleEntity
from taxation.domain.tax.value_objects.range import Range
from taxation.infrastructure.base_repository import BaseRepository
from taxation.models import TaxConfig, HotelTaxConfigMapping, SellerTaxConfigMapping
from taxation.models.tax_model import Tax, TaxableAmountRule, HotelTaxableAmountRuleMapping
from taxation.models.tax_model import TaxType


class TaxConfigRepository(BaseRepository):
    def get_tax_configs(self, category_ids, tax_types=None):
        results = self.session().query(TaxConfig).filter(TaxConfig.service_category_id.in_(category_ids)).options(
                                                            joinedload(TaxConfig.tax)).all()
        config_id_to_tax_config = dict()
        for result in results:
            if not tax_types or result.tax_type in tax_types:
                config_id_to_tax_config[result.tax_config_id] = self._to_tax_config_entity(result)
        return config_id_to_tax_config

    def get_tax_config_mapping_for_hotels(self, hotel_ids, tax_config_ids) -> Dict[str, List[int]]:
        results = self.session().query(HotelTaxConfigMapping)\
            .filter(HotelTaxConfigMapping.hotel_id.in_(hotel_ids))\
            .filter(HotelTaxConfigMapping.tax_config_id.in_(tax_config_ids))

        hotel_to_tax_config_ids_mapping = defaultdict(list)
        for result in results:
            hotel_to_tax_config_ids_mapping[result.hotel_id].append(result.tax_config_id)
        return hotel_to_tax_config_ids_mapping

    def get_tax_config_mapping_for_sellers(self, seller_ids, tax_config_ids) -> Dict[str, List[int]]:
        results = self.session().query(SellerTaxConfigMapping)\
            .filter(SellerTaxConfigMapping.seller_id.in_(seller_ids))\
            .filter(SellerTaxConfigMapping.tax_config_id.in_(tax_config_ids))

        seller_to_tax_config_ids_mapping = defaultdict(list)
        for result in results:
            seller_to_tax_config_ids_mapping[result.seller_id].append(result.tax_config_id)
        return seller_to_tax_config_ids_mapping

    @staticmethod
    def _to_tax_config_entity(model: TaxConfig):
        return TaxConfigV5(tax_config_id=model.tax_config_id, tax_id=model.tax_id, tax_code=model.tax.tax_code,
                           service_category=model.service_category_id, tax_type=TaxType(model.tax_type),
                           tax_value=model.tax_value, included_in_rate=model.included_in_rate,
                           date_range=Range(lower=model.effective_date, higher=model.expiry_date),
                           pretax_range=Range(lower=model.pretax_from, higher=model.pretax_to),
                           weekdays=model.weekdays)

    def get_all_tax_configs_by_state_id(self, state_id):
        if int(state_id) == constants.OTHER_TAX_SUPPORTED_CS_STATE_ID:
            tax_configs = self.session().query(TaxConfig).join(Tax).filter(
                or_(Tax.tax_code == "cgst", Tax.tax_code == "igst", Tax.tax_code == "sgst",
                    Tax.tax_code == "kerala_flood_cess")).all()
        else:
            tax_configs = self.session().query(TaxConfig).join(Tax).filter(
                or_(Tax.tax_code == "cgst", Tax.tax_code == "igst", Tax.tax_code == "sgst")).all()
        return tax_configs

    def get_taxable_amount_rules(self, category_ids) -> List[TaxableAmountRuleEntity]:
        results = self.session().query(TaxableAmountRule).filter(
            TaxableAmountRule.service_category_id.in_(category_ids)).all()
        return [TaxableAmountRuleEntity(rule_id=result.rule_id, service_category_id=result.service_category_id,
                                        percentage_of_total_value=result.percentage_of_total_value,
                                        minimum_taxable_amount=result.minimum_taxable_amount)
                for result in results]

    def get_hotel_to_rule_id_mapping(self, hotel_ids) -> Dict[str, List[int]]:
        results = self.session().query(HotelTaxableAmountRuleMapping).filter(
            HotelTaxableAmountRuleMapping.hotel_id.in_(hotel_ids)).all()
        hotel_to_rule_ids_mapping = defaultdict(list)
        for result in results:
            hotel_to_rule_ids_mapping[result.hotel_id].append(result.taxable_amount_rule_id)
        return hotel_to_rule_ids_mapping

    def get_tax_configs_for_hotel(self, hotel_id):
        tax_configs = (
            self.session()
            .query(TaxConfig)
            .join(HotelTaxConfigMapping, TaxConfig.tax_config_id == HotelTaxConfigMapping.tax_config_id)
            .filter(HotelTaxConfigMapping.hotel_id == hotel_id)
            .all()
        )
        return [self._to_tax_config_entity(tax_config) for tax_config in tax_configs]
