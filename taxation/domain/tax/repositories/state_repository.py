from sqlalchemy.orm.exc import MultipleResultsFound

from taxation.exceptions import MultipleResultsFound as TaxMultipleResultsFound
from taxation.infrastructure.base_repository import BaseRepository
from taxation.models import State


class StateRepository(BaseRepository):
    _model = State

    def fetch_state_by_state_id(self, state_id):
        return self.session().query(self._model).filter(self._model.state_id == state_id).first()

    def get_state_by_state_ids(self, state_ids):
        return self.session().query(self._model).filter(self._model.state_id.in_(state_ids)).all()

    def fetch_state_by_cs_state_id(self, cs_state_id):
        try:
            return self.session().query(State).filter(State.cs_state_id == cs_state_id).one_or_none()
        except MultipleResultsFound:
            raise TaxMultipleResultsFound("Multiple results found corresponding to cs_state_id: %s" % cs_state_id)

    def fetch_states_by_cs_state_ids(self, cs_state_ids):
        return self.session().query(State).filter(State.cs_state_id.in_(cs_state_ids)).all()
