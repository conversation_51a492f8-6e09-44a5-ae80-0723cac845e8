import logging
import os

import requests

logger = logging.getLogger(__name__)


class CacheApiClient(object):

    @classmethod
    def refresh_cache(cls, hotel):
        try:
            querystring = {"hotel_ids": str(hotel)}
            url = os.environ.get('PRICING_BASE_URL', 'http://localhost/v1') + '/cache_refresh'
            status = requests.get(url, params=querystring)
            return status
        except Exception:
            logger.exception("Error calling the redis-cache refresh API")
