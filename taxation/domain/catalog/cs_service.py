import logging
from collections import defaultdict

from treebo_commons.utils.config_value_parser import BooleanParser

from taxation import messages
from taxation.alerts import slack_alert
from taxation.exceptions import ResourceNotFound
from taxation.infrastructure.cs_client import CatalogingServiceApiClient

logger = logging.getLogger(__name__)


class SkuConstants:
    SKU_CODE = "code"
    SKU_TYPE = "sku_type"
    SKU_TAX_TYPE = "tax_type"
    SKU_CATEGORY_CODE = "sku_category_code"
    BUNDLED_SKU_TYPE = "bundle"
    STANDALONE_SKU_TYPE = "sku"
    DERIVED_TAX_TYPE = "derived"
    COMPOSITE_TAX_TYPE = "composite"
    SUB_SKUS = "skus"


class CatalogingService(object):
    def __init__(self):
        self.cs_client = CatalogingServiceApiClient()

    def get_category_list_from_sku_code(self, sku_code):

        sku_data = self.cs_client.get_sku_from_sku_code(sku_code)
        if sku_data[0]:

            return self.category_list_from_sku_data(sku_data[0])
        else:
            logger.error("Invalid sku code: %s", sku_code)
            slack_alert(data=sku_code, message=messages.INVALID_SKU_CODE)
            raise ResourceNotFound(messages.INVALID_SKU_CODE,
                                   context=dict(sku_code=sku_code))

    def get_all_sku_from_cataloging_service(self):

        return self.cs_client.get_all_sku()

    def get_sku_code_category_map_for_all_sku(self):

        sku_code_category_map_all = defaultdict(list)

        all_sku_data = self.get_all_sku_from_cataloging_service()

        for sku_data in all_sku_data:
            sku_code_category_map_all[sku_data[SkuConstants.SKU_CODE]].extend(
                self.category_list_from_sku_data(sku_data))
        return sku_code_category_map_all

    @staticmethod
    def category_list_from_sku_data(sku):

        category_list = []

        if sku[SkuConstants.SKU_TYPE] == SkuConstants.BUNDLED_SKU_TYPE:

            # Add another condition if new Bundled sku with tax category to be selected predefined is introduced later

            if sku[SkuConstants.SKU_TAX_TYPE] in [SkuConstants.DERIVED_TAX_TYPE, SkuConstants.COMPOSITE_TAX_TYPE]:

                for sub_sku in sku[SkuConstants.SUB_SKUS]:
                    category_list.append(sub_sku[SkuConstants.SKU_CATEGORY_CODE])
        else:
            category_list.append(sku[SkuConstants.SKU_CATEGORY_CODE])

        # remove duplicate categories from the list as it's redundant data
        category_list = list(set(category_list))

        return category_list

    def activate_sku_on_cataloging_service(self, cs_hotel_id, sku_codes):

        activate_sku_data_dict = dict()
        activate_sku_data_dict['cs_id'] = cs_hotel_id
        activate_sku_data_dict['sku_codes'] = sku_codes
        activate_sku_data_dict['service'] = "TAXATION"

        return self.cs_client.activate_sku(activate_sku_data_dict)

    def get_property_skus(self, cs_hotel_id):
        return self.cs_client.get_property_skus(cs_hotel_id)

    def is_onboarding_tax_enabled_via_consumer(self):
        response = self.cs_client.get_tenant_config()
        config_name_to_config_mapping = {config['config_name']: config for config in response}
        taxation_config = config_name_to_config_mapping.get('taxation_config.is_onboarding_tax_enabled_via_consumer')
        if not taxation_config:
            logger.info("Taxation config not present in Catalog for tenant")
            return False
        return BooleanParser().parse(taxation_config.get('config_value'))

