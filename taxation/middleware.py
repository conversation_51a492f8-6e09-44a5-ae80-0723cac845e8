import os
import logging

from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

logger = logging.getLogger(__name__)


def before_request():
    if 'ADMIN_TENANT_ID' in os.environ:
        logger.info("ADMIN_TENANT_ID: %s, found in environment. Overriding that in context.",
                    os.environ.get('ADMIN_TENANT_ID'))
        request_context.tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
