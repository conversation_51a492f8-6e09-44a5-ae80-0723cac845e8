# -*- coding: utf-8 -*-
"""Click commands."""
import logging
import os
from decimal import Decimal
from typing import List

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.utils import dateutils

from taxation import listutils
from taxation.consumer.catalog_consumer import CatalogServiceConsumer
from taxation.domain.tax import repo_provider
from taxation.domain.tax.entities.cgst_tax_config import CgstTaxConfig
from taxation.domain.tax.entities.other_tax_config import OtherTaxConfig
from taxation.domain.tax.entities.sgst_tax_config import SgstTaxConfig
from taxation.globals import consumer_context
from taxation.models import TaxConfig, HotelTaxConfigMapping, SellerTaxConfigMapping
from taxation.models.tax_model import TaxType, Weekdays

HERE = os.path.abspath(os.path.dirname(__file__))
PROJECT_ROOT = os.path.join(HERE, os.pardir)
TEST_PATH = os.path.join(PROJECT_ROOT, 'tests')

logger = logging.getLogger(__name__)


@click.command(name='start_catalog_consumer')
@click.option('--tenant_id', help="Tenant ID for which this command should be run.",
              default=TenantClient.get_default_tenant())
@with_appcontext
def start_catalog_consumer(tenant_id=TenantClient.get_default_tenant()):
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id=tenant_id)
    consumer = CatalogServiceConsumer(tenant_id=tenant_id)
    consumer.start_consumer()


@click.command(name='migrate_tax_model')
@with_appcontext
def migrate_tax_model():
    hotels = repo_provider.hotel_config_repository.fetch_all()
    hotels_by_state_id = listutils.convert_to_multivalue_dict(hotels, key=lambda item: item.state_id)
    sellers = repo_provider.seller_repository.fetch_all()
    sellers_by_state_id = listutils.convert_to_multivalue_dict(sellers, key=lambda item: item.state_id)
    state_ids = list({hotel.state_id for hotel in hotels})
    states = repo_provider.state_repository.get_state_by_state_ids(state_ids)
    services = repo_provider.service_repository.get_services()
    cgst, sgst, other_tax = repo_provider.gst_repository.get_tax_config_for_given_state_and_service(
        service_ids=[s.id for s in services], state_ids=[int(s.state_id) for s in states])
    print("Total cgst: {0}, total sgst: {1}, total other tax: {2}".format(len(cgst), len(sgst), len(other_tax)))

    cgst_config = CgstTaxConfig(cgst)
    sgst_config = SgstTaxConfig(sgst)
    other_tax_config = OtherTaxConfig(other_tax)

    taxes = repo_provider.tax_repository.fetch_all()
    tax_by_code = {tax.tax_code: tax for tax in taxes}

    logger.info("Tax by code: %s", tax_by_code)

    if not taxes:
        return

    kerala_state_id = next(state.state_id for state in states if state.state_name.lower() == 'kerala')

    for service in services:
        cgst_for_service = cgst_config.get_cgst(service.id)
        # SGST config is going to be same for all the states
        logger.info("Reading sgst tax config for service: %s and state: %s", service.id, states[0].state_id)
        sgst_for_service = sgst_config.get_sgst(states[0].state_id, service.id)
        if not sgst_for_service:
            logger.info("Didn't find SGST for service: %s and state: %s", service.id, states[0].state_id)
            if not cgst_for_service:
                logger.info("Didn't find CGST for service: %s. Skipping Tax Config for service", service.id)
                continue

        other_taxes_for_service = other_tax_config.get_other_tax(kerala_state_id, service.id)

        cgst_tax_configs = create_cgst_tax_configs(cgst_for_service, service, tax_by_code)
        sgst_tax_configs = create_sgst_tax_configs(sgst_for_service, service, tax_by_code)
        igst_tax_configs = create_igst_tax_configs(cgst_tax_configs, sgst_tax_configs, service, tax_by_code)
        kerala_flood_cess_tag_configs = create_kerala_flood_cess_tax_config(other_taxes_for_service, service,
                                                                            tax_by_code)

        for state in states:
            state_id = state.state_id
            map_tax_configs_to_hotels(cgst_tax_configs,
                                      kerala_flood_cess_tag_configs if state_id == kerala_state_id else None,
                                      sgst_tax_configs,
                                      igst_tax_configs, hotels_by_state_id[state_id])
            map_tax_configs_to_sellers(cgst_tax_configs,
                                       kerala_flood_cess_tag_configs if state_id == kerala_state_id else None,
                                       sgst_tax_configs,
                                       igst_tax_configs, sellers_by_state_id[state_id])

        repo_provider.tax_config_repository.create_all(cgst_tax_configs)
        repo_provider.tax_config_repository.create_all(sgst_tax_configs)
        repo_provider.tax_config_repository.create_all(kerala_flood_cess_tag_configs)
        repo_provider.tax_config_repository.create_all(igst_tax_configs)


def create_kerala_flood_cess_tax_config(other_taxes_for_state, service, tax_by_code) -> List[TaxConfig]:
    other_tax_configs = []
    for other_tax in other_taxes_for_state:
        date_range = get_date_range(other_tax.effective_date, other_tax.expiry_date)
        pretax_range = get_price_range(other_tax.from_price_pretax, other_tax.to_price_pretax)
        tax_config = TaxConfig(tax_id=tax_by_code.get('kerala_flood_cess').tax_id,
                               service_category_id=service.category_id,
                               tax_type=TaxType.PERCENTAGE.value, date_range=date_range,
                               pretax_range=pretax_range,
                               tax_value=other_tax.tax_percent,
                               included_in_rate=False,
                               weekdays_byte=Weekdays.get_byte_for_all_days())
        other_tax_configs.append(tax_config)
    return other_tax_configs


def create_sgst_tax_configs(sgst_for_service, service, tax_by_code) -> List[TaxConfig]:
    sgst_tax_configs = []
    for sgst in sgst_for_service:
        date_range = get_date_range(sgst.effective_date, sgst.expiry_date)
        pretax_range = get_price_range(sgst.from_price_pretax, sgst.to_price_pretax)
        tax_config = TaxConfig(tax_id=tax_by_code.get('sgst').tax_id, service_category_id=service.category_id,
                               tax_type=TaxType.PERCENTAGE.value, date_range=date_range,
                               pretax_range=pretax_range,
                               tax_value=sgst.sgst_percent, included_in_rate=False,
                               weekdays_byte=Weekdays.get_byte_for_all_days())
        sgst_tax_configs.append(tax_config)
    return sgst_tax_configs


def create_cgst_tax_configs(cgst_for_service, service, tax_by_code) -> List[TaxConfig]:
    cgst_tax_configs = []
    for cgst in cgst_for_service:
        date_range = get_date_range(cgst.effective_date, cgst.expiry_date)
        pretax_range = get_price_range(cgst.from_price_pretax, cgst.to_price_pretax)
        tax_config = TaxConfig(tax_id=tax_by_code.get('cgst').tax_id, service_category_id=service.category_id,
                               tax_type=TaxType.PERCENTAGE.value, date_range=date_range,
                               pretax_range=pretax_range,
                               tax_value=cgst.cgst_percent, included_in_rate=False,
                               weekdays_byte=Weekdays.get_byte_for_all_days())
        cgst_tax_configs.append(tax_config)
    return cgst_tax_configs


def create_igst_tax_configs(cgst_tax_configs, sgst_tax_configs, service, tax_by_code):
    # Indexing the sgst_tax_configs by 2 level, first key being `date_range`, and second key being `pretax_range`
    # Assumption is, CGST and SGST should have 1-to-1 parallels for the same date_range and price_ranges
    sgst_tax_configs_by_date_and_pretax_range = listutils.group_list(sgst_tax_configs, lambda item: item.date_range,
                                                                     lambda item: item.pretax_range, nesting_level=2)
    igst_tax_configs = []
    for cgst_tax_config in cgst_tax_configs:
        sgst_tax_config = sgst_tax_configs_by_date_and_pretax_range[cgst_tax_config.date_range][
            cgst_tax_config.pretax_range]

        # For this CGST Tax Config, there should be exactly 1 SGST Tax Config, with same date_range and pretax_range
        assert len(sgst_tax_config) == 1, "Found 0 or more than 1 sgst tax config for cgst tax config: {0}".format(
            cgst_tax_config)

        sgst_tax_config = sgst_tax_config[0]
        tax_config = TaxConfig(tax_id=tax_by_code.get('igst').tax_id, service_category_id=service.category_id,
                               tax_type=TaxType.PERCENTAGE.value, date_range=cgst_tax_config.date_range,
                               pretax_range=cgst_tax_config.pretax_range,
                               tax_value=cgst_tax_config.tax_value + sgst_tax_config.tax_value,
                               included_in_rate=False,
                               weekdays_byte=Weekdays.get_byte_for_all_days())
        igst_tax_configs.append(tax_config)
    return igst_tax_configs


def get_date_range(from_date, to_date):
    from psycopg2._range import DateRange
    to_date = None if to_date > dateutils.to_date(dateutils.today_plus_days(1)) else to_date
    return DateRange(from_date, to_date)


def get_price_range(from_price, to_price):
    from psycopg2._range import NumericRange
    if from_price is None and to_price is None:
        return NumericRange(None, None)
    if from_price == Decimal('0.00') and to_price > Decimal('20000'):
        return NumericRange(None, None)
    elif to_price > Decimal('20000'):
        return NumericRange(from_price, None)
    else:
        return NumericRange(from_price, to_price)


def map_tax_configs_to_hotels(cgst_tax_configs, kerala_flood_cess_tag_configs, sgst_tax_configs, igst_tax_configs,
                              hotels):
    for hotel in hotels:
        if hotel.churned or not hotel.cs_hotel_id:
            continue
        for tax_config in cgst_tax_configs + sgst_tax_configs + igst_tax_configs:
            tax_config.hotel_tax_configs.append(HotelTaxConfigMapping(hotel=hotel, hotel_id=hotel.cs_hotel_id))

        if kerala_flood_cess_tag_configs:
            for tax_config in kerala_flood_cess_tag_configs:
                tax_config.hotel_tax_configs.append(HotelTaxConfigMapping(hotel=hotel, hotel_id=hotel.cs_hotel_id))


def map_tax_configs_to_sellers(cgst_tax_configs, kerala_flood_cess_tag_configs, sgst_tax_configs, igst_tax_configs,
                               sellers):
    for seller in sellers:
        for tax_config in cgst_tax_configs + sgst_tax_configs + igst_tax_configs:
            tax_config.seller_tax_configs.append(SellerTaxConfigMapping(seller=seller))

        if kerala_flood_cess_tag_configs:
            for tax_config in kerala_flood_cess_tag_configs:
                tax_config.seller_tax_configs.append(SellerTaxConfigMapping(seller=seller))
