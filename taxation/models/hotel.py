import logging

from sqlalchemy import Enum, ForeignKey, UniqueConstraint, Column, INTEGER, String, Integer, Boolean, DateTime, func

__all__ = ['HotelConfig', 'State', 'RoomConfig']

from sqlalchemy.orm import relationship

from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

logger = logging.getLogger(__name__)


class State(Base):
    __tablename__ = 'taxation_state'

    id = Column('id', INTEGER, primary_key=True)
    state_id = Column('state_id', INTEGER, nullable=True, unique=True)
    cs_state_id = Column('cs_state_id', String, nullable=True, unique=True)
    state_name = Column('state_name', String, nullable=False)

    def __str__(self):
        return "Web_state_id: %s, name: %s, cs_state_id: %s" % (self.state_id, self.state_name, self.cs_state_id)


class HotelConfig(Base):
    __tablename__ = 'taxation_hotel_config'

    ARR_TAX_PRETAX_PRICE_SELECTOR = ['Always', 'RoomPrice lt ARR', 'RoomPrice gt ARR']

    id = Column('id', Integer, primary_key=True)
    hotel_id = Column('hotel_id', Integer, nullable=True, unique=True)
    hx_id = Column('hx_id', String, nullable=True)
    cs_hotel_id = Column('cs_hotel_id', String, nullable=True, unique=True)
    state_id = Column('state_id', Integer, nullable=True)
    state_identifier = Column('state_identifier', Integer, ForeignKey(State.id, onupdate='CASCADE'),
                              server_default="1", nullable=True)
    hotel_name = Column('hotel_name', String, nullable=False)
    hotel_luxury_tax_enabled = Column('hotel_luxury_tax_enabled', Boolean)
    arr_tax_enabled = Column('arr_tax_enabled', Boolean)
    declared_tariff_enabled = Column('declared_tariff_enabled', Boolean, default=True)
    use_arr_for_tax_calculation = Column('use_arr_for_tax_calculation', Enum(*ARR_TAX_PRETAX_PRICE_SELECTOR,
                                                                             name='arr_tax_pretax_price_selector'))
    churned = Column('churned', Boolean, default=False)

    state = relationship("State", foreign_keys=state_identifier)

    def __str__(self):
        return '%s (cs_hotel_id: %s)' % (self.hotel_name, self.cs_hotel_id)

    def __repr__(self):
        return '%s (cs_hotel_id: %s)' % (self.hotel_name, self.cs_hotel_id)


class RoomConfig(Base):
    __tablename__ = 'taxation_room_config'

    id = Column('id', Integer, primary_key=True)
    cs_hotel_id = Column('cs_hotel_id', String, nullable=False)
    name = Column('name', String, nullable=False, index=True)
    room_code = Column('room_code', String, nullable=False, index=True)
    created_at = Column('created_at', DateTime, server_default=func.now(), nullable=False)
    modified_at = Column('modified_at', DateTime, server_default=func.now(),
                         server_onupdate=func.now(), onupdate=func.now(), nullable=False)
    __table_args__ = (UniqueConstraint('cs_hotel_id', 'room_code', name='_cs_hotel_id_and_room_code_uc'),)

    def __str__(self):
        return 'Name: %s, cs_hotel_id: %s, room_code: %s' % (self.name, self.cs_hotel_id, self.room_code)

    def __repr__(self):
        return 'Name: %s, cs_hotel_id: %s, room_code: %s' % (self.name, self.cs_hotel_id, self.room_code)
