from enum import Enum

from sqlalchemy import Index, Column, Integer, String, ForeignKey, DECIMAL, Boolean
from sqlalchemy.dialects.postgresql import NUMRANGE, DATERANGE
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from taxation.models import HotelConfig, Service
from taxation.models.seller import Seller


class Tax(Base):
    __tablename__ = 'taxation_tax'

    tax_id = Column(Integer, primary_key=True)
    tax_code = Column(String, unique=True)
    name = Column(String)
    description = Column(String)
    unit = Column(String)

    def __str__(self):
        return "{0} (per {1})".format(self.tax_code, self.unit)


class TaxType(Enum):
    FLAT = "FLAT"
    PERCENTAGE = "PERCENT"
    TIERED = "TIERED"

    def __str__(self):
        return self.value  # value string

    def __html__(self):
        return self.value  # option labels


class TaxConfig(Base):
    __tablename__ = 'taxation_tax_config'

    tax_config_id = Column(Integer, primary_key=True)
    tax_id = Column(Integer, ForeignKey(Tax.tax_id), nullable=False)
    service_category_id = Column(String, ForeignKey(Service.category_id))
    tax_type = Column(String, nullable=False)
    # https://bitbucket.org/zzzeek/sqlalchemy/src/55eacc8dbea3c3f98197bde9034fd6558fb2bc09/test/dialect/postgresql
    # #/test_types.py?at=master#cl-1378
    # Above test case is for various range types in postgres
    date_range = Column(DATERANGE, nullable=False)
    pretax_range = Column(NUMRANGE, nullable=False)
    tax_value = Column(DECIMAL, nullable=False)
    included_in_rate = Column(Boolean)
    # Storing weekdays as byte, default is all days
    # https://stackoverflow.com/a/24174625/9885477
    weekdays_byte = Column(Integer, nullable=True, default=127)
    available_for_all_hotels = Column(Boolean, nullable=False, default=True)

    tax = relationship("Tax", foreign_keys=tax_id, lazy='joined', innerjoin=True)
    service = relationship("Service", foreign_keys=service_category_id)

    hotel_tax_configs = relationship("HotelTaxConfigMapping")
    seller_tax_configs = relationship("SellerTaxConfigMapping")

    __table_args__ = (
        Index('ix_tax_config_tax_code_service_category', 'tax_id', 'service_category_id'),
    )

    @property
    def effective_date(self):
        return self.date_range.lower

    @property
    def expiry_date(self):
        return self.date_range.upper

    @property
    def pretax_from(self):
        return self.pretax_range.lower

    @property
    def pretax_to(self):
        return self.pretax_range.upper

    @property
    def weekdays(self):
        return [b for a, b in Weekdays.__members__.items() if (self.weekdays_byte & b.value) > 0]

    def __str__(self):
        return "[{0}] {1}@{2} (effective {3}, expiry {4})".format(self.tax_config_id, self.tax.tax_code,
                                                                  self.service_category_id,
                                                                  self.date_range.lower, self.date_range.upper)

    def __repr__(self):
        return "[{0}] {1}@{2} (effective {3}, expiry {4})".format(self.tax_config_id, self.tax.tax_code,
                                                                  self.service_category_id,
                                                                  self.date_range.lower, self.date_range.upper)


class Weekdays(Enum):
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 4
    THURSDAY = 8
    FRIDAY = 16
    SATURDAY = 32
    SUNDAY = 64

    @staticmethod
    def get_byte_for_all_days():
        return sum([byte_value.value for weekday, byte_value in Weekdays.__members__.items()])


class HotelTaxConfigMapping(Base):
    __tablename__ = 'taxation_hotel_tax_config_mapping'

    hotel_id = Column(String, ForeignKey(HotelConfig.cs_hotel_id), primary_key=True)
    tax_config_id = Column(Integer, ForeignKey(TaxConfig.tax_config_id), primary_key=True)

    tax_config = relationship("TaxConfig", foreign_keys=tax_config_id)
    hotel = relationship("HotelConfig", foreign_keys=hotel_id)


class SellerTaxConfigMapping(Base):
    __tablename__ = 'taxation_seller_tax_config_mapping'

    seller_id = Column(String, ForeignKey(Seller.seller_id), primary_key=True)
    tax_config_id = Column(Integer, ForeignKey(TaxConfig.tax_config_id), primary_key=True)

    tax_config = relationship("TaxConfig", foreign_keys=tax_config_id)
    seller = relationship("Seller", foreign_keys=seller_id)


class TaxableAmountRule(Base):
    __tablename__ = 'taxation_taxable_amount_rule'

    rule_id = Column(Integer, primary_key=True)
    service_category_id = Column(String, ForeignKey(Service.category_id))
    percentage_of_total_value = Column(DECIMAL)
    minimum_taxable_amount = Column(DECIMAL)

    service = relationship("Service", foreign_keys=service_category_id)

    def __str__(self):
        return "Rule: {0} ({1})".format(self.rule_id, self.service.name)

    def __repr__(self):
        return "Rule: {0} ({1})".format(self.rule_id, self.service.name)


class HotelTaxableAmountRuleMapping(Base):
    __tablename__ = 'taxation_hotel_taxable_amount_rule_mapping'

    hotel_id = Column(String, ForeignKey(HotelConfig.cs_hotel_id), primary_key=True)
    taxable_amount_rule_id = Column(Integer, ForeignKey(TaxableAmountRule.rule_id), primary_key=True)

    taxable_amount_rule = relationship("TaxableAmountRule", foreign_keys=taxable_amount_rule_id)
    hotel = relationship("HotelConfig", foreign_keys=hotel_id)

    def __str__(self):
        return "Hotel: {0} | Rule: {1} ({2})".format(self.hotel_id, self.taxable_amount_rule_id,
                                                     self.taxable_amount_rule.service.name)

    def __repr__(self):
        return "Hotel: {0} | Rule: {1} ({2})".format(self.hotel_id, self.taxable_amount_rule_id,
                                                     self.taxable_amount_rule.service.name)
