from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from sqlalchemy import Enum, Index, Column, INTEGER, DateTime, func, String

__all__ = ['GlobalConfig']


class GlobalConfig(Base):
    __tablename__ = 'taxation_global_config'
    CONFIG_DATA_TYPE_CODES = ['text', 'int', 'float', 'boolean', 'date', 'time', 'timestamp']
    STATUS_TYPE_CODES = ['active', 'inactive', 'deleted']

    id = Column('id', INTEGER, primary_key=True)
    created_at = Column('created_at', DateTime, server_default=func.now(), index=True)
    modified_at = Column('modified_at', DateTime, server_default=func.now(),
                         server_onupdate=func.now(), onupdate=func.now(), index=True, nullable=False)
    config_data_type = Column('config_data_type', Enum(*CONFIG_DATA_TYPE_CODES, name='config_data_type'),
                              nullable=False)
    config_name = Column('config_name', String, nullable=False, index=True)
    config_value = Column('config_value', String, nullable=False)
    status = Column('status', Enum(*STATUS_TYPE_CODES, name='status_type'), nullable=False, index=True)

    __table_args__ = (Index('taxation_global_config_index', "config_name", "modified_at", "created_at", "status"),)

    def __str__(self):
        return "{0}: {1}".format(self.config_name, self.config_value)

    def __repr__(self):
        return "config_name:{0}, config_value:{1}, config_data_type:{2}, modified_at:{3}, status:{4}".format(
            self.config_name, self.config_value,
            self.config_data_type, self.modified_at, self.status)
