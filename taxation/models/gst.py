import logging

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DECIMAL, Date
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from taxation.models import Service, State

__all__ = ['CGST', 'SGST', 'OtherTax', 'TaxType']

logger = logging.getLogger(__name__)


class CGST(Base):
    __tablename__ = 'taxation_cgst'

    id = Column('id', Integer, primary_key=True)
    service_name = Column('service_name', String, nullable=False)
    service_id = Column('service_id', Integer, ForeignKey(Service.id, onupdate='CASCADE'), server_default="1",
                        nullable=False)
    from_price_pretax = Column('from_price_pretax', DECIMAL(precision=19, scale=4), nullable=False)
    to_price_pretax = Column('to_price_pretax', DECIMAL(precision=19, scale=4), nullable=False)
    from_price_posttax = Column('from_price_posttax', DECIMAL(precision=19, scale=4), nullable=True)
    to_price_posttax = Column('to_price_posttax', DECIMAL(precision=19, scale=4), nullable=True)
    effective_date = Column('effective_date', Date, nullable=False)
    expiry_date = Column('expiry_date', Date, nullable=False)
    cgst_percent = Column('cgst_percent', DECIMAL(precision=19, scale=4), nullable=False)

    service = relationship("Service", foreign_keys=service_id)

    def __str__(self):
        return '%s. %s' % (self.id, self.service_name)


class SGST(Base):
    __tablename__ = 'taxation_sgst'

    id = Column('id', Integer, primary_key=True)
    service_name = Column('service_name', String, nullable=False)
    service_id = Column('service_id', Integer, ForeignKey(Service.id, onupdate='CASCADE'), server_default="1",
                        nullable=False)
    state_id = Column('state_id', Integer, nullable=False)
    state_identifier = Column('state_identifier', Integer, ForeignKey(State.id, onupdate='CASCADE'),
                              server_default="1", nullable=False)
    from_price_pretax = Column('from_price_pretax', DECIMAL(precision=19, scale=4), nullable=False)
    to_price_pretax = Column('to_price_pretax', DECIMAL(precision=19, scale=4), nullable=False)
    from_price_posttax = Column('from_price_posttax', DECIMAL(precision=19, scale=4), nullable=True)
    to_price_posttax = Column('to_price_posttax', DECIMAL(precision=19, scale=4), nullable=True)
    effective_date = Column('effective_date', Date, nullable=False)
    expiry_date = Column('expiry_date', Date, nullable=False)
    sgst_percent = Column('sgst_percent', DECIMAL(precision=19, scale=4), nullable=False)
    gst_percent = Column('gst_percent', DECIMAL(precision=19, scale=4), nullable=False)

    state = relationship('State', foreign_keys=state_identifier)
    service = relationship("Service", foreign_keys=service_id)

    def __str__(self):
        return '%s. %s' % (self.id, self.service_name)


class TaxType(Base):
    __tablename__ = 'taxation_tax_type'
    id = Column('id', Integer, primary_key=True)
    name = Column('name', String, nullable=False, unique=True)

    def __str__(self):
        return '%s' % self.name


class OtherTax(Base):
    __tablename__ = 'taxation_other_tax'

    id = Column('id', Integer, primary_key=True)
    service_name = Column('service_name', String, nullable=False)
    service_id = Column('service_id', Integer, ForeignKey(Service.id, onupdate='CASCADE'), server_default="1",
                        nullable=False)
    state_id = Column('state_id', Integer, nullable=False)
    state_identifier = Column('state_identifier', Integer, ForeignKey(State.id, onupdate='CASCADE'),
                              server_default="1", nullable=False)
    from_price_pretax = Column('from_price_pretax', DECIMAL(precision=19, scale=4), nullable=False)
    to_price_pretax = Column('to_price_pretax', DECIMAL(precision=19, scale=4), nullable=False)
    from_price_posttax = Column('from_price_posttax', DECIMAL(precision=19, scale=4), nullable=True)
    to_price_posttax = Column('to_price_posttax', DECIMAL(precision=19, scale=4), nullable=True)
    effective_date = Column('effective_date', Date, nullable=False)
    expiry_date = Column('expiry_date', Date, nullable=False)
    tax_percent = Column('tax_percent', DECIMAL(precision=19, scale=4), nullable=False)
    tax_type_id = Column('tax_type_id', Integer, ForeignKey(TaxType.id, onupdate='CASCADE'), server_default="1",
                         nullable=False)
    tax_type_name = Column('tax_type_name', String, nullable=True)
    state = relationship('State', foreign_keys=state_identifier)
    service = relationship("Service", foreign_keys=service_id)
    tax_type = relationship("TaxType", foreign_keys=tax_type_id)

    def __str__(self):
        return '%s. %s' % (self.id, self.tax_type)
