from sqlalchemy import Column, String, Integer
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base


class Seller(Base):
    __tablename__ = 'taxation_seller'

    seller_id = Column(String, primary_key=True)
    name = Column(String)
    category = Column(String)
    state_id = Column(Integer, nullable=True)

    def __str__(self):
        return '%s (seller_id: %s)' % (self.name, self.seller_id)

    def __repr__(self):
        return '%s (seller_id: %s)' % (self.name, self.seller_id)
