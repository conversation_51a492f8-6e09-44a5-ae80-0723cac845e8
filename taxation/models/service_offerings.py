import logging


__all__ = ['Service']

from sqlalchemy import Column, Integer, String, Boolean
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

logger = logging.getLogger(__name__)


class Service(Base):
    __tablename__ = 'taxation_service'
    # id, service_accounting_code, category_id, name, declared_tariff_required
    id = Column('id', Integer, primary_key=True)
    name = Column('name', String, nullable=False)
    service_accounting_code = Column('service_accounting_code', String, nullable=True)
    category_id = Column('category_id', String, nullable=True, unique=True)
    declared_tariff_required = Column('declared_tariff_required', Boolean, default=False)

    def __str__(self):
        return '%s (HSN: %s)' % (self.name, self.service_accounting_code)
