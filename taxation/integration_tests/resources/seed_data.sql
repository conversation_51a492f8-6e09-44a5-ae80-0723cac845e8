INSERT INTO "public"."taxation_state"("id", "state_id", "state_name", "cs_state_id")
VALUES (1, 1, 'Test', '1');

INSERT INTO "public"."taxation_hotel_config"("id", "hotel_id", "hotel_name", "hotel_luxury_tax_enabled",
"arr_tax_enabled", "use_arr_for_tax_calculation", "state_id", "declared_tariff_enabled", "churned", "hx_id",
"cs_hotel_id", "state_identifier")
VALUES (1, 1, 'Test Treebo Hotel', 'f', 'f', NULL, 1, 'f', 'f', '1', '0001661', 1),
(2, 2, 'Test Treebo Kerela Hotel', 'f', 'f', NULL, 1, 'f', 'f', '1', '0016932', 1),
(3, 3, 'Test Hotel', 'f', 'f', NULL, 1, 'f', 'f', '1', '0001825', 1);


INSERT INTO "public"."taxation_seller"("seller_id", "name", "category", "state_id")
VALUES (1, 'Treebo POS', 'food', 1);

INSERT INTO "public"."taxation_tax"("tax_id", "tax_code", "description", "unit", "name")
VALUES (1, 'cgst', 'CGST', 'room', 'CGST'),
(2, 'sgst', 'SGST', 'room', 'SGST'),
(3, 'igst', 'IGST', 'room', 'IGST'),
(4, 'kerela_flood_cess', 'Kerela Flood Cess', 'room', 'Kerela Flood Cess'),
(5, 'VAT-FLAT', 'VAT-FLAT', 'room', 'VAT-FLAT'),
(6, 'VAT-PERCENTAGE', 'VAT-PERCENTAGE', 'room', 'VAT-PERCENTAGE'),
(7, 'VAT-FLAT_IIR', 'VAT-FLAT_IIR', 'room', 'FLAT_IIR'),
(8, 'VAT-PERCENTAGE-IIR', 'VAT-PERCENTAGE-IIR', 'room', 'VAT-PERCENTAGE-IIR');

INSERT INTO "public"."taxation_service"("id", "name", "service_accounting_code", "category_id",
"declared_tariff_required")
VALUES (1, 'stay', '996311', 'stay', 't'),
(2, 'food', '996312', 'food', 't'),
(3, 'roomnight', '996313', 'roomnight', 't'),
(4, 'laundry', '996314', 'laundry', 't'),
(5, 'currency_exchange','996315','currency_exchange','t');

INSERT INTO "public"."taxation_tax_config"("tax_config_id", "service_category_id", "tax_type", "date_range",
"pretax_range", "tax_value", "included_in_rate", "weekdays_byte", "available_for_all_hotels", "tax_id")
VALUES (1, 'stay', 'PERCENT', '[2019-10-01,)', '[0.0000, 1000.0001)', 0.0000, 'f', 127, 't', 1),
(2, 'stay', 'PERCENT', '[2019-10-01,)', '[1000.0001, 7500.0001)', 6.0000, 'f', 127, 't', 1),
(3, 'stay', 'PERCENT', '[2019-10-01,)', '[7500.0001,)', 9.0000, 'f', 127, 't', 1),
(4, 'stay', 'PERCENT', '[2019-10-01,)', '[0.0000, 1000.0001)', 0.0000, 'f', 127, 't', 2),
(5, 'stay', 'PERCENT', '[2019-10-01,)', '[1000.0001, 7500.0001)', 6.0000, 'f', 127, 't', 2),
(6, 'stay', 'PERCENT', '[2019-10-01,)', '[7500.0001,)', 9.0000, 'f', 127, 't', 2),
(7, 'stay', 'PERCENT', '[2019-10-01,)', '[0.0000, 1000.0001)', 0.0000, 'f', 127, 't', 3),
(8, 'stay', 'PERCENT', '[2019-10-01,)', '[1000.0001, 7500.0001)', 12.0000, 'f', 127, 't', 3),
(9, 'stay', 'PERCENT', '[2019-10-01,)', '[7500.0001,)', 18.0000, 'f', 127, 't', 3),
(10, 'stay', 'PERCENT', '[2019-10-01,)', '[,)', 1.0000, 'f', 127, 't', 4),
(11, 'food', 'PERCENT', '[2019-10-01,)', '[,)', '2.5000', 'f', 127, 't', 1),
(12, 'food', 'PERCENT', '[2019-10-01,)', '[,)', '2.5000', 'f', 127, 't', 2),
(13, 'food', 'PERCENT', '[2019-10-01,)', '[,)', '5.0000', 'f', 127, 't', 3),
(14, 'food', 'FLAT', '[2019-10-01,)', '[,)', '1000', 'f', 127, 't', 5),
(15, 'roomnight', 'FLAT', '[2019-10-01,)', '[,)', '100', 'f', 127, 't', 5),
(16, 'roomnight', 'PERCENT', '[2019-10-01,)', '[,)', '12.0000', 'f', 127, 't', 6),
(17, 'stay', 'FLAT', '[2019-10-01,)', '[,)', '100', 't', 127, 't', 7),
(18, 'stay', 'PERCENT', '[2019-10-01,)', '[,)', '12.0000', 't', 127, 't', 8),
(19, 'laundry', 'FLAT', '[2019-10-01,)', '[,)', '100', 'f', 127, 't', 7),
(20, 'laundry', 'PERCENT', '[2019-10-01,)', '[,)', '12.0000', 't', 127, 't', 8),
(21, 'currency_exchange', 'FLAT', '[2019-10-01,)', '[,)', '100', 'f', 127, 't', 5),
(22, 'currency_exchange', 'PERCENT', '[2019-10-01,)', '[,)', '12.0000', 'f', 127, 't', 6);

INSERT INTO "public"."taxation_hotel_tax_config_mapping"("hotel_id", "tax_config_id")
VALUES ('0001661', 1), ('0001661', 2), ('0001661', 3), ('0001661', 4), ('0001661', 5), ('0001661', 6), ('0001661', 7), (
'0001661', 8), ('0001661', 9), ('0001661', 11), ('0001661', 12), ('0001661', 13), ('0016932', 2), ('0016932', 5), (
'0016932', 10), ('0001825', 14), ('0001825', 15), ('0001825', 16), ('0001825', 17), ('0001825', 18), ('0001825', 19), (
'0001825', 20), ('0001825', 21), ('0001825', 22), ('0001661',21);

INSERT INTO "public"."taxation_seller_tax_config_mapping"("seller_id", "tax_config_id")
VALUES ('1', 11), ('1', 12), ('1', 13);

INSERT INTO "public"."taxation_taxable_amount_rule"("rule_id", "service_category_id", "percentage_of_total_value","minimum_taxable_amount")
VALUES ('1', 'currency_exchange','1','250');

INSERT INTO "public"."taxation_hotel_taxable_amount_rule_mapping"("hotel_id", "taxable_amount_rule_id")
VALUES ('0001825','1');
