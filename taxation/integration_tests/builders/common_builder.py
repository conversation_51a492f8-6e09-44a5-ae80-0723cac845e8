from taxation.integration_tests.config.sheet_names import attribute_sheet_name, prices_sheet_name
from taxation.integration_tests.utilities import excel_utils
from taxation.integration_tests.utilities.common_utils import return_date


class Skus:
    def __init__(self, skus_data):
        self.category_id = skus_data['sku_category']
        self.index = skus_data['index']
        self.attributes = []
        if skus_data['attributes']:
            self.attributes = [
                Attributes(excel_utils.get_test_case_data(attribute_sheet_name, attributes_data)[0]).__dict__ for
                attributes_data in skus_data['attributes'].split(',')]
        self.prices = [Prices(excel_utils.get_test_case_data(prices_sheet_name, price_data)[0]).__dict__ for price_data
                       in
                       skus_data['prices'].split(',')]


class Attributes:
    def __init__(self, attributes_data):
        self.key = attributes_data['key']
        self.value = str(attributes_data['value'])


class Prices:
    def __init__(self, prices_data):
        self.date = str(return_date(prices_data['date']))
        self.index = prices_data['index']
        if prices_data['posttax_price']:
            self.posttax_price = prices_data['posttax_price']
        if prices_data['pretax_price']:
            self.pretax_price = prices_data['pretax_price']
