from flask import json
import logging

from taxation.integration_tests.utilities.common_utils import assert_

logger = logging.getLogger(__name__)


class BaseRequest(object):
    def __init__(self):
        self.debtor_id = None
        self.debit_id = []
        self.credit_id = None
        self.credit_reference_number = None

    def request_processor(self, client_, request_type, url, status_code, request_json=None, parameters=None):
        headers = {'Content-Type': 'application/json'}
        client_type = {
            "POST": client_.post,
            "PATCH": client_.patch,
            "GET": client_.get,
            "DELETE": client_.delete
        }

        print('\n\n' + '#' * 25 + 'REQUEST' + '#' * 25)
        print('REQUEST URL: ' + url + '\nREQUEST TYPE: ' + request_type + '\nHEADERS: ' + str(headers) +
              '\nREQUEST JSON: ' + str(json.dumps(json.loads(request_json))) + '\nREQUEST PARAMS: ' + str(parameters))
        response = client_type.get(request_type)(
            url,
            data=json.dumps(json.loads(request_json)),
            headers=headers
        )
        print('\n\n' + '#' * 25 + 'RESPONSE' + '#' * 25)
        print(
            'RESPONSE CODE: ' + str(response.status_code) + '\nRESPONSE DATA: ' + json.dumps(json.loads(response.data)))
        assert_(response.status_code, status_code, 'Status code is not matching')
        return json.dumps(json.loads(response.data))
