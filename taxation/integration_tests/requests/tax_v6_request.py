import json

from taxation.integration_tests.builders import tax_v6_builder
from taxation.integration_tests.config.request_uris import tax_v6_uri
from taxation.integration_tests.config.sheet_names import tax_v6_sheet_name
from taxation.integration_tests.requests.base_request import BaseRequest
from taxation.integration_tests.utilities.common_utils import del_none


class TaxV6Request(BaseRequest):
    def create_tax_v6_request(self, client_, test_case_id, status_code):
        request_json = json.dumps(del_none(tax_v6_builder.TaxV6Builder
                                           (sheet_name=tax_v6_sheet_name, test_case_id=test_case_id).__dict__))
        response = self.request_processor(client_, 'POST', tax_v6_uri, status_code, request_json)
        return response
