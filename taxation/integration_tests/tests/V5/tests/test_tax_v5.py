import pytest

from taxation.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from taxation.integration_tests.requests.tax_v5_request import TaxV5Request
from taxation.integration_tests.tests.V5.validations.validate_tax_v5 import ValidationTaxV5
from taxation.integration_tests.tests.base_test import response_validation_negative_cases


class TestTaxV5:
    @pytest.mark.parametrize(
        "test_case_id,tc_description,status_code, error_message, skip_message", [
            ("TaxV5_01", "Calculate tax for stay category with pretax amount=500", 200, "", ""),
            ("TaxV5_02", "Calculate tax for stay category with pretax amount=1000", 200, "", ""),
            ("TaxV5_03", "Calculate tax for stay category with pretax amount=1000.01", 200, "", ""),
            ("TaxV5_04", "Calculate tax for stay category with pretax amount=2000", 200, "", ""),
            ("TaxV5_05", "Calculate tax for stay category with pretax amount=7500", 200, "", ""),
            ("TaxV5_06", "Calculate tax for stay category with pretax amount=7500.01", 200, "", ""),
            ("TaxV5_07", "Calculate tax for stay category with pretax amount=9000", 200, "", ""),
            ("TaxV5_08", "Calculate tax for stay category with posttax amount=1000", 200, "", ""),
            ("TaxV5_09", "Calculate tax for stay category with posttax amount=1120", 200, "", ""),
            ("TaxV5_10", "Calculate tax for stay category with posttax amount=1120.01", 200, "", ""),
            ("TaxV5_11", "Calculate tax for stay category with posttax amount=2240", 200, "", ""),
            ("TaxV5_12", "Calculate tax for stay category with posttax amount=8400", 200, "", ""),
            ("TaxV5_13", "Calculate tax for stay category with posttax amount=8850", 200, "", ""),
            ("TaxV5_14", "Calculate tax for stay category with posttax amount=8850.01", 200, "", ""),
            ("TaxV5_15", "Calculate tax for stay category with posttax amount=10000", 200, "", ""),
            ("TaxV5_16", "Calculate tax with posttax amount=2240 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV5_17", "Calculate tax with pretax amount=2000 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV5_18", "Calculate tax with posttax amount=2240 with buyer_has_lut true", 200, "", ""),
            ("TaxV5_19", "Calculate tax with pretax amount=2000 with buyer_has_lut true", 200, "", ""),
            ("TaxV5_20", "Calculate tax with posttax amount=2240 with seller_has_lut true", 200, "", ""),
            ("TaxV5_21", "Calculate tax with pretax amount=2000 with seller_has_lut true", 200, "", ""),
            ("TaxV5_22", "Calculate tax with posttax amount=2240 with buyer_has_lut ,seller_has_lut true", 200, "", ""),
            ("TaxV5_23", "Calculate tax with pretax amount=2000 with buyer_has_lut, seller_has_lut true", 200, "", ""),
            ("TaxV5_24", "Calculate tax with kerala cess in it for posttax amount = 2240", 200, "", ""),
            ("TaxV5_25", "Calculate tax with kerala cess in it for pretax amount = 2000", 200, "", ""),
            ("TaxV5_26", "Calculate tax for food for pretax amount = 1000", 200, "", ""),
            ("TaxV5_27", "Calculate tax for food for posttax amount = 1000", 200, "", ""),
            ("TaxV5_28", "Calculate tax for food with posttax amount=2240 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV5_29", "Calculate tax for food with pretax amount=2000 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV5_30", "Calculate tax for food with posttax amount=2240 with buyer_has_lut true", 200, "", ""),
            ("TaxV5_31", "Calculate tax for food with pretax amount=2000 with buyer_has_lut true", 200, "", ""),
            ("TaxV5_32", "Calculate tax for food with posttax amount=2240 with seller_has_lut true", 200, "", ""),
            ("TaxV5_33", "Calculate tax for food with pretax amount=2000 with seller_has_lut true", 200, "", ""),
            ("TaxV5_34", "Calculate tax for food with posttax amount=2240 with both lut true", 200, "", ""),
            ("TaxV5_35", "Calculate tax for food with pretax amount=2000 with both lut true", 200, "", ""),
            ("TaxV5_36", "Calculate tax for food for pretax amount = 1000 for pos seller", 200, "", ""),
            ("TaxV5_37", "Calculate tax for food for posttax amount = 1000 for seller", 200, "", ""),
            ("TaxV5_38", "calculate tax for tax type as FLAT for pretax amount = 1000", 200, "", ""),
            ("TaxV5_39", "calculate tax for tax type as FLAT for posttax amount = 1000", 200, "", ""),
            ("TaxV5_40", "calculate tax for tax type as FLAT and PERCENT for pretax amount = 1000", 200, "", ""),
            ("TaxV5_41", "calculate tax for tax type as FLAT and PERCENT for posttax amount = 1000", 200, "", ""),
            ("TaxV5_42", "tax for pretax amount = 1000 with included in rate=true for both category", 200, "", ""),
            ("TaxV5_43", "tax for posttax amount = 1000 with included in rate=true for both category", 200, "", ""),
            ("TaxV5_44", "tax for pretax amount = 1000 with included in rate=true for one category", 200, "", ""),
            ("TaxV5_45", "tax for posttax amount = 1000 with included in rate=true for one category", 200, "", ""),
            ("TaxV5_46", "calculate tax with taxation rule of rs 250 for 1 percent for pretax amount=100", 200, "", ""),
            ("TaxV5_47", "calculate tax with taxation rule of rs 250 for 1 percent for pretax amount=250", 200, "", ""),
            ("TaxV5_48", "calculate tax with taxation rule of rs 500 for 1 percent for pretax amount=500", 200, "", ""),
            ("TaxV5_49", "calculate tax ç taxation rule of rs 250 for 1 percent for posttax amount=100", 200, "", ""),
            ("TaxV5_50", "calculate tax ç taxation rule of rs 250 for 1 percent for posttax amount=250", 200, "", ""),
            ("TaxV5_51", "calculate tax ç taxation rule of rs 500 for 1 percent for posttax amount=1000", 200, "", ""),
            ("TaxV5_52", "calculate tax with pretax amount=100 and taxation rule not configured ç hotel", 200, "", ""),
            ("TaxV5_53", "calculate tax with pretax amount=200 for multiple dates", 200, "", ""),
            ("TaxV5_54", "calculate tax with pretax amount=200 for multiple dates for multiple sku's", 200, "", ""),
            ("TaxV5_55", "Caclualte tax without attributes", 400, "", ""),
            ("TaxV5_56", "calculate tax without seller id and hotel id", 400, "", ""),
            ("TaxV5_57", "calculate tax without sku category id", 200, "", ""),
            ("TaxV5_58", "calculate tax with invalid sku category id", 200, "", ""),
            ("TaxV5_59", "calculate tax with invalid dates in price", 200, "", ""),
            ("TaxV5_60", "calculate tax with Negative Pretax price", 200, "", ""),
            ("TaxV5_61", "calculate tax with Zero Pretax price", 200, "", ""),
            ("TaxV5_62", "calculate tax with Negative Posttax price", 200, "", ""),
            ("TaxV5_63", "calculate tax with Zero Posttax price", 200, "", ""),
        ])
    @pytest.mark.regression
    def test_tax_v5(self, client_, test_case_id, tc_description, status_code, error_message, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        response = TaxV5Request().create_tax_v5_request(client_, test_case_id, status_code)

        if status_code in ERROR_CODES:
            response_validation_negative_cases(response, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"


    @staticmethod
    def validation(response, test_case_id):
        ValidationTaxV5(response, test_case_id).validate_reponse()
