import json
from taxation.integration_tests.config.sheet_names import tax_v5_sheet_name, prices_sheet_name
from taxation.integration_tests.utilities import excel_utils
from taxation.integration_tests.utilities.common_utils import assert_, return_date, sort_dict


class ValidationTaxV5:
    def __init__(self, response, test_case_id):
        self.response = json.loads(response)
        self.test_case_id = test_case_id
        self.test_data = excel_utils.get_test_case_data(tax_v5_sheet_name, test_case_id)[0]

    def validate_reponse(self):
        assert_(self.response['skus'][0]['index'], self.test_data['index'])
        for price_index, price_tc_id in enumerate(self.test_data['prices'].split(",")):
            price_test_data = excel_utils.get_test_case_data(prices_sheet_name, price_tc_id)[0]
            assert_(self.response['skus'][0]['prices'][price_index]['date'], str(return_date(price_test_data['date'])))
            if price_test_data['pretax_price'] and self.test_case_id not in ['TaxV5_42', 'TaxV5_44']:
                assert_(float(self.response['skus'][0]['prices'][price_index]['pretax_price']),
                        float(price_test_data['pretax_price']))
            if price_test_data['posttax_price']:
                assert_(float(self.response['skus'][0]['prices'][price_index]['posttax_price']),
                        float(price_test_data['posttax_price']))

            assert_(sort_dict(self.response['skus'][0]['prices'][price_index]['tax_breakup']),
                    sort_dict(json.loads(self.test_data['expected_response'])['skus'][0]['prices'][price_index][
                                  'tax_breakup']))
            assert_(self.response['skus'][0]['prices'][price_index]['taxable_amount'],
                    json.loads(self.test_data['expected_response'])['skus'][0]['prices'][price_index]['taxable_amount'])
            assert_(self.response['skus'][0]['prices'][price_index]['tax_amount'],
                    json.loads(self.test_data['expected_response'])['skus'][0]['prices'][price_index]['tax_amount'])
            assert_(self.response['skus'][0]['prices'][price_index]['pretax_price'],
                    json.loads(self.test_data['expected_response'])['skus'][0]['prices'][price_index]['pretax_price'])
            assert_(self.response['skus'][0]['prices'][price_index]['posttax_price'],
                    json.loads(self.test_data['expected_response'])['skus'][0]['prices'][price_index]['posttax_price'])
