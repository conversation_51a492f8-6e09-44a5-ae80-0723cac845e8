import pytest

from taxation.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES
from taxation.integration_tests.requests.tax_v6_request import TaxV6Request
from taxation.integration_tests.tests.V6.validations.validate_tax_v6 import ValidationTaxV6
from taxation.integration_tests.tests.base_test import response_validation_negative_cases


class TestTaxV6:
    @pytest.mark.parametrize(
        "test_case_id,tc_description,status_code, error_message, skip_message", [
            ("TaxV6_01", "Calculate tax for stay category with pretax amount=500", 200, "", ""),
            ("TaxV6_02", "Calculate tax for stay category with pretax amount=1000", 200, "", ""),
            ("TaxV6_03", "Calculate tax for stay category with pretax amount=1000.01", 200, "", ""),
            ("TaxV6_04", "Calculate tax for stay category with pretax amount=2000", 200, "", ""),
            ("TaxV6_05", "Calculate tax for stay category with pretax amount=7500", 200, "", ""),
            ("TaxV6_06", "Calculate tax for stay category with pretax amount=7500.01", 200, "", ""),
            ("TaxV6_07", "Calculate tax for stay category with pretax amount=9000", 200, "", ""),
            ("TaxV6_08", "Calculate tax for stay category with posttax amount=1000", 200, "", ""),
            ("TaxV6_09", "Calculate tax for stay category with posttax amount=1120", 200, "", ""),
            ("TaxV6_10", "Calculate tax for stay category with posttax amount=1120.01", 200, "", ""),
            ("TaxV6_11", "Calculate tax for stay category with posttax amount=2240", 200, "", ""),
            ("TaxV6_12", "Calculate tax for stay category with posttax amount=8400", 200, "", ""),
            ("TaxV6_13", "Calculate tax for stay category with posttax amount=8850", 200, "", ""),
            ("TaxV6_14", "Calculate tax for stay category with posttax amount=8850.01", 200, "", ""),
            ("TaxV6_15", "Calculate tax for stay category with posttax amount=10000", 200, "", ""),
            ("TaxV6_16", "Calculate tax with posttax amount=2240 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV6_17", "Calculate tax with pretax amount=2000 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV6_18", "Calculate tax with posttax amount=2240 with buyer_has_lut true", 200, "", ""),
            ("TaxV6_19", "Calculate tax with pretax amount=2000 with buyer_has_lut true", 200, "", ""),
            ("TaxV6_20", "Calculate tax with posttax amount=2240 with seller_has_lut true", 200, "", ""),
            ("TaxV6_21", "Calculate tax with pretax amount=2000 with seller_has_lut true", 200, "", ""),
            ("TaxV6_22", "Calculate tax with posttax amount=2240 with buyer_has_lut ,seller_has_lut true", 200, "", ""),
            ("TaxV6_23", "Calculate tax with pretax amount=2000 with buyer_has_lut, seller_has_lut true", 200, "", ""),
            ("TaxV6_26", "Calculate tax for food for pretax amount = 1000", 200, "", ""),
            ("TaxV6_27", "Calculate tax for food for posttax amount = 1000", 200, "", ""),
            ("TaxV6_28", "Calculate tax for food with posttax amount=2240 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV6_29", "Calculate tax for food with pretax amount=2000 with is_buyer_in_sez true", 200, "", ""),
            ("TaxV6_30", "Calculate tax for food with posttax amount=2240 with buyer_has_lut true", 200, "", ""),
            ("TaxV6_31", "Calculate tax for food with pretax amount=2000 with buyer_has_lut true", 200, "", ""),
            ("TaxV6_32", "Calculate tax for food with posttax amount=2240 with seller_has_lut true", 200, "", ""),
            ("TaxV6_33", "Calculate tax for food with pretax amount=2000 with seller_has_lut true", 200, "", ""),
            ("TaxV6_34", "Calculate tax for food with posttax amount=2240 with both lut true", 200, "", ""),
            ("TaxV6_35", "Calculate tax for food with pretax amount=2000 with both lut true", 200, "", ""),
            ("TaxV6_36", "Calculate tax for food for pretax amount = 1000 for pos seller", 200, "", ""),
            ("TaxV6_37", "Calculate tax for food for posttax amount = 1000 for seller", 200, "", ""),
            ("TaxV6_53", "calculate tax with pretax amount=200 for multiple dates", 200, "", ""),
            ("TaxV6_54", "calculate tax with pretax amount=200 for multiple dates for multiple sku's", 200, "", ""),
            ("TaxV6_55", "Caclualte tax without attributes", 400, "", ""),
            ("TaxV6_56", "calculate tax without seller id and hotel id", 400, "", ""),
            ("TaxV6_57", "calculate tax without sku category id", 200, "", ""),
            ("TaxV6_58", "calculate tax with invalid sku category id", 200, "", ""),
            ("TaxV6_59", "calculate tax with invalid dates in price", 200, "", ""),
            ("TaxV6_60", "calculate tax with Negative Pretax price", 200, "", ""),
            ("TaxV6_61", "calculate tax with Zero Pretax price", 200, "", ""),
            ("TaxV6_62", "calculate tax with Negative Posttax price", 200, "", ""),
            ("TaxV6_63", "calculate tax with Zero Posttax price", 200, "", ""),
        ])
    @pytest.mark.regression
    def test_tax_v6(self, client_, test_case_id, tc_description, status_code, error_message, skip_message):
        if skip_message:
            pytest.skip(skip_message)

        response = TaxV6Request().create_tax_v6_request(client_, test_case_id, status_code)

        if status_code in ERROR_CODES:
            response_validation_negative_cases(response, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"


    @staticmethod
    def validation(response, test_case_id):
        ValidationTaxV6(response, test_case_id).validate_reponse()
