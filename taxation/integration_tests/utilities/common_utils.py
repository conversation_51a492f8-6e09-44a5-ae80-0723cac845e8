import json
from datetime import timedelta, date

from treebo_commons.multitenancy.sqlalchemy import db_engine


def return_date(days):
    return date.today() + timedelta(days=int(days))


# todo: with time completely replace sanitize_blank method with sanitize_test_data
# sanitize the values for blank fields from excel
def sanitize_blank(value):
    if value == '' or value == 'null' or value == {} or value == 'BLANK' or value == 'NULL':
        return None
    else:
        return value


# interpret the values for NULL/EMPTY/blank fields from excel
def sanitize_test_data(data):
    if data == '' or data == 'null':
        return None
    elif data == 'NULL':
        return 'NULL_KEY'
    elif data == 'EMPTY':
        return ''
    elif isinstance(data, dict):
        if all(value is None for value in data.values()):
            return None
        elif all(value == 'NULL_KEY' or value is None for value in data.values()):
            return 'NULL_KEY'
        else:
            return data
    else:
        return data


# for deleting the null values and empty dict from object passed
def del_none(dict_object):
    for key, value in list(dict_object.items()):
        if isinstance(value, dict):
            del_none(value)
        elif isinstance(value, list):
            for val in value:
                if isinstance(val, dict):
                    del_none(val)
        if value is None:
            dict_object[key] = None

    return dict_object


def assert_(actual_value, expected_value, failure_message=None):
    assert sanitize_test_data(actual_value) == sanitize_test_data(expected_value), str(
        failure_message) + ". ACTUAL: " + str(
        actual_value) + " EXPECTED: " + str(expected_value)


def query_execute(query, commit_required=True):
    result = db_engine.get_session(None).execute(query)
    if commit_required:
        db_engine.get_session(None).commit()
    return result


# Sort nested dictionary with keys and value. This also insures dictionaries within lists are also sorted.
def sort_dict(dict_value):
    if isinstance(dict_value, dict):
        return {k: sort_dict(v) for k, v in sorted(dict_value.items())}
    elif isinstance(dict_value, list):
        return sorted((sort_dict(i) for i in dict_value), key=lambda x: json.dumps(x, sort_keys=True))
    else:
        return dict_value
