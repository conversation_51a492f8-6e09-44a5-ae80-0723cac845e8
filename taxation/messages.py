from collections import namedtuple

Error = namedtuple('Error', ['message', 'code'])

API_REQUEST_DATA_VALIDATION_FAILED = Error(message='Invalid data received in the API request',
                                           code=1000)
STATE_TAX_CONFIG_MISSING = Error(message='State Tax is not configured for the given product configuration',
                                 code=1001)
STATE_LUXURY_TAX_CONFIG_MISSING = Error(
    message='State Luxury Tax is not configured for the given product configuration',
    code=1002)
DUPLICATE_STATE_LUXURY_TAX = Error(message='Multiple State Luxury Tax is configured for given product configuration',
                                   code=1003)
HOTEL_LUXURY_TAX_CONFIG_MISSING = Error(
    message='Hotel Luxury Tax is not configured for the given product configuration',
    code=1004)
DUPLICATE_HOTEL_LUXURY_TAX = Error(message='Multiple Hotel Luxury Tax is configured for given product configuration',
                                   code=1005)
ARR_LUXURY_TAX_CONFIG_MISSING = Error(message='ARR Luxury Tax is not configured for the given product configuration',
                                      code=1006)
DUPLICATE_ARR_LUXURY_TAX = Error(message='Multiple ARR Luxury Tax is configured for given product configuration',
                                 code=1007)
CGST_CONFIG_MISSING = Error(message='CGST is not configured for the given product configuration',
                            code=1008)
SGST_CONFIG_MISSING = Error(message='SGST is not configured for the given product configuration',
                            code=1009)
DECLARED_TARIFF_CONFIG_MISSING = Error(message='DeclaredTariff is not configured for given product configuration',
                                       code=1010)
DECLARED_TARIFF_DATA_ERROR = Error(message='Invalid data for declared tariff', code=1011)

DECLARED_TARIFF_DATE_RANGE_ERROR = Error(message='DT: date range error', code=1012)
DT_NO_OF_HOTEL_ERROR = Error(message='Number of hotels in a request shouldn\'t exceed 10', code=1013)
DT_NO_DATA_ERROR = Error(message='Parameter shouldn\'t be null or empty', code=1014)

DATE_FORMAT_ERROR = Error(message='Invalid date format, correct format: yyyy-mm-dd', code=1015)

INACTIVE_HOTEL_ERROR = Error(message='Hotel is not live in system', code=1016)

ROOM_DETAIL_REQUEST_DATA_ERROR = Error(message='Room with the given room data is not configured', code=1017)
OCCUPANCY_REQUEST_DATA_ERROR = Error(message='Occupancy with the given room data is not configured', code=1023)

HOTEL_SYNC_ERROR = Error(message='Hotel sync failed', code=1018)

ROOM_SYNC_ERROR = Error(message='Room sync failed', code=1019)
SERVICE_SYNC_ERROR = Error(message='Service sync failed', code=1020)

CATEGORY_MISSING = Error(message='Categories with the given category data is not configured',
                         code=1021)

ROOM_CODES_MISSING = Error(message='Room Codes with the given room code data is not configured',
                           code=1022)
DATA_FORMAT_ERROR = Error(message='Data format error', code=1024)
ROOM_REQUEST_DATA_MISSING_ERROR = Error(message='Room data missing in request', code=1025)
OCCUPANCY_REQUEST_DATA_MISSING_ERROR = Error(message='Occupancy data missing in request', code=1026)
INVALID_POST_TAX_AMOUNT = Error(message='Invalid post tax amount in request', code=1027)
DECLARED_TARIFF_BREACHED = Error(message='Post tax price is higher that declared tariff, breach detected for the given product configuration.',
                                 code=1028)
API_REQUEST_PROPERTY_DATA_ERROR = Error(message='Invalid data received in the API request, Hotel data missing', code=1029)
API_REQUEST_SKU_DATA_ERROR = Error(message='Invalid data received in the API request, SKU data missing', code=1030)
HOTEL_CONFIG_MISSING = Error(message='Hotel with the given hotel data is not configured',
                             code=2000)
# HOTEL_CONFIG_MISSING = Error(message='Hotel with the given hotel_id is not configured',
#                              code=2000)
INVALID_PRODUCT_TYPE = Error(message='Invalid Product Type received',
                             code=2001)

INVALID_SKU_CODE = Error(message='Invalid SKU code', code=2002)
