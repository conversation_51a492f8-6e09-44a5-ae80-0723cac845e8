from contextlib import ContextDecorator
from sqlalchemy.exc import DBAPIError
from treebo_commons.multitenancy.sqlalchemy import db_engine


class CleanupSession(ContextDecorator):
    def __init__(self, session=db_engine.get_scoped_session()):
        self.session = session

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type and issubclass(exc_type, DBAPIError):
            self.session.remove()
