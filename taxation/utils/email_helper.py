from flask_mail import Mail, Message
import os
from flask import current_app
from .dateutils import current_date_in_utc


def send_missing_declared_tariff_csv(filename):
    mail = Mail(current_app)
    current_date = str(current_date_in_utc())
    msg = Message('Missing declared tariff report for {0}'.format(current_date),
                  sender='<EMAIL>',
                  recipients=current_app.config.get('MISSING_DECLARED_TARIFF_REPORT_MAIL_LIST'))

    with current_app.open_resource(filename) as fp:
        file_title = filename.split('/')[-1]
        msg.attach(file_title, 'text/csv', fp.read())
    mail.send(msg)
    os.remove(os.path.join(current_app.config.get('BASE_DIR'), 'taxation', filename))
