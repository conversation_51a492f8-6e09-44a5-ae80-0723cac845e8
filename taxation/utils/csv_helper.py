from flask import current_app
import os
from .dateutils import current_date_in_utc


def create_missing_declared_tariff_csv(sku_dt_dict):
    import csv
    headers = ['hotel_id', 'hx_id', 'hotel_name', 'room_type_code', 'occupancy', 'from_date', 'to_date']
    current_date = str(current_date_in_utc())
    filename = os.path.join(current_app.config.get('BASE_DIR'), 'missing_declared_tariff-' + current_date + '-.csv')
    with open(filename, 'w') as resultFile:
        wr = csv.writer(resultFile, dialect='excel')
        wr.writerow(headers)
        for sku, date_ranges in sku_dt_dict.items():
            for date_range in date_ranges:
                from_date, to_date = date_range[0], date_range[1]
                row = []
                row.append(sku[0])
                row.append(sku[1])
                row.append(sku[2])
                row.append(sku[3])
                row.append(sku[4])
                row.append(str(from_date))
                row.append(str(to_date))
                wr.writerow(row)

    return filename
