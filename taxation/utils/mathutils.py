from decimal import Decimal

from taxation.numberutils import ZERO


def compute_tax_from_posttax(price, cgst_percent, sgst_percent, igst_percent, other_tax_percent=None):
    other_tax_value = 0
    p = price
    base_value = Decimal('100') + cgst_percent + sgst_percent + igst_percent + (
        Decimal(other_tax_percent) if other_tax_percent else ZERO)
    cgst_value = cgst_percent * p / base_value
    sgst_value = sgst_percent * p / base_value
    igst_value = igst_percent * p / base_value
    if other_tax_percent:
        other_tax_value = other_tax_percent * p / base_value
    return round(cgst_value, 2), round(sgst_value, 2), round(igst_value, 2), round(other_tax_value, 2)
