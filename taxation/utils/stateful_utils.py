from flask import current_app

from taxation.utils.dateutils import ymdhm_str_to_datetime, current_datetime_in_india_timezone


def is_price_based_compute_applicable():
    price_based_compute_start_date = ymdhm_str_to_datetime(current_app.config.get('PRICE_BASED_COMPUTE_START_DATE'))
    current_date_in_IST = current_datetime_in_india_timezone()

    return current_date_in_IST >= price_based_compute_start_date


def is_kerala_gstin(gstin):
    KERALA_STATE_CODE = '32'
    if not is_valid_gstin(gstin):
        return False
    return gstin.startswith(KERALA_STATE_CODE)


def is_valid_gstin(gstin: str) -> bool:
    return (
        gstin is not None
        and gstin.isalnum()
        and gstin[:2].isnumeric()
        and len(gstin) == 15
    )
