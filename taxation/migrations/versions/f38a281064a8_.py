"""empty message

Revision ID: f38a281064a8
Revises: 190f9fa8c375
Create Date: 2017-07-25 15:16:56.652112

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f38a281064a8'
down_revision = '190f9fa8c375'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_hotel_config', sa.Column('declared_tariff_enabled', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('taxation_hotel_config', 'declared_tariff_enabled')
    # ### end Alembic commands ###
