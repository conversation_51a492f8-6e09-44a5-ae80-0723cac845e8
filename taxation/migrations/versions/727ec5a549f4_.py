"""empty message

Revision ID: 727ec5a549f4
Revises:
Create Date: 2017-03-04 13:07:09.793305

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '727ec5a549f4'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_hotel_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.Integer(), nullable=False),
    sa.Column('hotel_name', sa.String(), nullable=False),
    sa.Column('hotel_luxury_tax_enabled', sa.<PERSON>(), nullable=True),
    sa.Column('arr_tax_enabled', sa.<PERSON>(), nullable=True),
    sa.Column('use_arr_for_tax_calculation', sa.Enum('Always', 'RoomPrice lt ARR', 'RoomPrice gt ARR', name='arr_tax_pretax_price_selector'), nullable=True),
    sa.<PERSON>ey<PERSON>onstraint('id'),
    sa.UniqueConstraint('hotel_id'),
    sa.UniqueConstraint('hotel_name')
    )
    op.create_table('taxation_state_luxurytax',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('state_id', sa.Integer(), nullable=False),
    sa.Column('from_price', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('tax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('taxation_statetax',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('state_id', sa.Integer(), nullable=False),
    sa.Column('tax_type', sa.Enum('ServiceTax', 'SwatchBharatCess', 'KrishiKalyanCess', name='tax_types'), nullable=False),
    sa.Column('tax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('effective', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('taxation_arr_luxurytax',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.Integer(), nullable=False),
    sa.Column('room_type_code', sa.Enum('acacia', 'oak', 'maple', 'mahogany', name='room_type_codes'), nullable=False),
    sa.Column('occupancy', sa.Integer(), nullable=False),
    sa.Column('average_room_rate', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('tax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.ForeignKeyConstraint(['hotel_id'], ['taxation_hotel_config.hotel_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('taxation_hotel_luxurytax',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('hotel_id', sa.Integer(), nullable=False),
    sa.Column('from_price', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('tax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.ForeignKeyConstraint(['hotel_id'], ['taxation_hotel_config.hotel_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('taxation_hotel_luxurytax')
    op.drop_table('taxation_arr_luxurytax')
    op.drop_table('taxation_statetax')
    op.drop_table('taxation_state_luxurytax')
    op.drop_table('taxation_hotel_config')
    # ### end Alembic commands ###
