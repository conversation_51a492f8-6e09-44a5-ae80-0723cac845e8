"""empty message

Revision ID: 0e33760affc8
Revises: 727ec5a549f4
Create Date: 2017-05-04 21:50:24.743015

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0e33760affc8'
down_revision = '727ec5a549f4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_state',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('state_id', sa.INTEGER(), nullable=False),
    sa.Column('state_name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('state_id')
    )
    # op.drop_constraint('taxation_arr_luxurytax_hotel_id_fkey', 'taxation_arr_luxurytax', type_='foreignkey')
    # op.create_foreign_key(None, 'taxation_arr_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'], onupdate='CASCADE')
    # op.add_column('taxation_hotel_config', sa.Column('state_id', sa.Integer(), nullable=True))
    # op.create_foreign_key(None, 'taxation_hotel_config', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    # op.drop_constraint('taxation_hotel_luxurytax_hotel_id_fkey', 'taxation_hotel_luxurytax', type_='foreignkey')
    # op.create_foreign_key(None, 'taxation_hotel_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'], onupdate='CASCADE')
    # op.create_foreign_key(None, 'taxation_state_luxurytax', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    # op.create_foreign_key(None, 'taxation_statetax', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.drop_constraint(None, 'taxation_statetax', type_='foreignkey')
    # op.drop_constraint(None, 'taxation_state_luxurytax', type_='foreignkey')
    # op.drop_constraint(None, 'taxation_hotel_luxurytax', type_='foreignkey')
    # op.create_foreign_key('taxation_hotel_luxurytax_hotel_id_fkey', 'taxation_hotel_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'])
    # op.drop_constraint(None, 'taxation_hotel_config', type_='foreignkey')
    # op.drop_column('taxation_hotel_config', 'state_id')
    # op.drop_constraint(None, 'taxation_arr_luxurytax', type_='foreignkey')
    # op.create_foreign_key('taxation_arr_luxurytax_hotel_id_fkey', 'taxation_arr_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'])
    op.drop_table('taxation_state')
    # ### end Alembic commands ###
