"""empty message

Revision ID: e32fec05320a
Revises: 04fe62ff1200
Create Date: 2020-07-20 11:32:31.875056

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e32fec05320a'
down_revision = '04fe62ff1200'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_seller', sa.Column('state_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('taxation_seller', 'state_id')
    # ### end Alembic commands ###
