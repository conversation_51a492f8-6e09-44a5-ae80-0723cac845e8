"""empty message

Revision ID: 5395af79963f
Revises: ad9273ff3790
Create Date: 2018-06-19 07:11:41.400906

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5395af79963f'
down_revision = 'ad9273ff3790'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('taxation_cgst_service_name_fkey', 'taxation_cgst', type_='foreignkey')
    op.drop_constraint('taxation_sgst_service_name_fkey', 'taxation_sgst', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('taxation_sgst_service_name_fkey', 'taxation_sgst', 'taxation_service', ['service_name'], ['name'], onupdate='CASCADE')
    op.create_foreign_key('taxation_cgst_service_name_fkey', 'taxation_cgst', 'taxation_service', ['service_name'], ['name'], onupdate='CASCADE')
    # ### end Alembic commands ###
