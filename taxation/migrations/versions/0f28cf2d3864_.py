"""empty message

Revision ID: 0f28cf2d3864
Revises: 14ea04209264
Create Date: 2020-07-14 11:35:37.334137

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0f28cf2d3864'
down_revision = '14ea04209264'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_seller',
    sa.Column('seller_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('seller_id')
    )
    op.create_table('taxation_seller_tax_config_mapping',
    sa.Column('seller_id', sa.String(), nullable=False),
    sa.Column('tax_config_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['seller_id'], ['taxation_seller.seller_id'], ),
    sa.ForeignKeyConstraint(['tax_config_id'], ['taxation_tax_config.tax_config_id'], ),
    sa.PrimaryKeyConstraint('seller_id', 'tax_config_id')
    )
    op.add_column('taxation_tax', sa.Column('name', sa.String(), nullable=True))
    op.add_column('taxation_tax_config', sa.Column('weekdays_byte', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('taxation_tax_config', 'weekdays_byte')
    op.drop_column('taxation_tax', 'name')
    op.drop_table('taxation_seller_tax_config_mapping')
    op.drop_table('taxation_seller')
    # ### end Alembic commands ###
