"""empty message

Revision ID: f13a4a906757
Revises: 5395af79963f
Create Date: 2018-06-19 07:31:42.112600

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f13a4a906757'
down_revision = '5395af79963f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('taxation_sgst_state_id_fkey', 'taxation_sgst', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('taxation_sgst_state_id_fkey', 'taxation_sgst', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    # ### end Alembic commands ###
