"""empty message

Revision ID: 7091e915a502
Revises: 0b8ab0197186
Create Date: 2018-01-30 08:49:43.143474

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7091e915a502'
down_revision = '0b8ab0197186'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_dt_packet',
    sa.<PERSON>umn('id', sa.INTEGER(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('modified_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('filename', sa.String(), nullable=False),
    sa.Column('packet_id', sa.Integer(), nullable=False),
    sa.Column('data', sa.String(), nullable=False),
    sa.Column('hotel_count', sa.Integer(), nullable=False),
    sa.Column('room_type_count', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('taxation_dt_packet_index', 'taxation_dt_packet', ['filename', 'modified_at', 'status'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('taxation_dt_packet_index', table_name='taxation_dt_packet')
    op.drop_table('taxation_dt_packet')
    # ### end Alembic commands ###
