"""empty message

Revision ID: 86b14a0a328e
Revises: c5214838f45a
Create Date: 2019-06-04 10:21:01.536138

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '86b14a0a328e'
down_revision = 'c5214838f45a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_other_tax', sa.Column('tax_percent', sa.DECIMAL(precision=19, scale=4), nullable=False))
    op.drop_column('taxation_other_tax', 'sgst_percent')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_other_tax', sa.Column('sgst_percent', sa.NUMERIC(precision=19, scale=4), autoincrement=False, nullable=False))
    op.drop_column('taxation_other_tax', 'tax_percent')
    # ### end Alembic commands ###
