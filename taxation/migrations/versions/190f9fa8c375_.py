"""empty message

Revision ID: 190f9fa8c375
Revises: 55ca29d1962e
Create Date: 2017-06-30 14:52:11.530442

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '190f9fa8c375'
down_revision = '55ca29d1962e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_declared_tariff',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('modified_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('hotel_id', sa.Integer(), nullable=False),
    sa.Column('room_type_code', sa.Enum('acacia', 'oak', 'maple', 'mahogany', name='room_codes_new'), nullable=False),
    sa.Column('occupancy', sa.Integer(), nullable=False),
    sa.Column('from_date', sa.Date(), nullable=False),
    sa.Column('to_date', sa.Date(), nullable=False),
    sa.Column('declared_tariff', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.ForeignKeyConstraint(['hotel_id'], ['taxation_hotel_config.hotel_id'], onupdate='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_taxation_declared_tariff_created_at'), 'taxation_declared_tariff', ['created_at'], unique=False)
    op.create_index(op.f('ix_taxation_declared_tariff_modified_at'), 'taxation_declared_tariff', ['modified_at'], unique=False)
    op.create_index('taxation_declared_tariff_index', 'taxation_declared_tariff', ['hotel_id', 'room_type_code', 'from_date', 'to_date'], unique=False)
    op.add_column('taxation_service', sa.Column('service_accounting_code', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'taxation_service', ['service_accounting_code'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'taxation_service', type_='unique')
    op.drop_column('taxation_service', 'service_accounting_code')
    op.drop_index('taxation_declared_tariff_index', table_name='taxation_declared_tariff')
    op.drop_index(op.f('ix_taxation_declared_tariff_modified_at'), table_name='taxation_declared_tariff')
    op.drop_index(op.f('ix_taxation_declared_tariff_created_at'), table_name='taxation_declared_tariff')
    op.drop_table('taxation_declared_tariff')
    # ### end Alembic commands ###
