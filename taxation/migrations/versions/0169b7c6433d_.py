"""empty message

Revision ID: 0169b7c6433d
Revises: 3bd31ef6718e
Create Date: 2018-04-25 20:13:43.719237

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0169b7c6433d'
down_revision = '3bd31ef6718e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_room_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('cs_hotel_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('room_code', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_taxation_room_config_name'), 'taxation_room_config', ['name'], unique=False)
    op.create_index(op.f('ix_taxation_room_config_room_code'), 'taxation_room_config', ['room_code'], unique=False)
    op.add_column('taxation_cgst', sa.Column('service_id', sa.Integer(), server_default='1', nullable=False))
    op.create_foreign_key(None, 'taxation_cgst', 'taxation_service', ['service_id'], ['id'], onupdate='CASCADE')
    op.add_column('taxation_declared_tariff', sa.Column('hotel_identifier', sa.Integer(), server_default='1', nullable=False))
    op.alter_column('taxation_declared_tariff', 'modified_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.create_foreign_key(None, 'taxation_declared_tariff', 'taxation_hotel_config', ['hotel_identifier'], ['id'], onupdate='CASCADE')
    op.add_column('taxation_hotel_config', sa.Column('cs_hotel_id', sa.String(), nullable=True))
    op.add_column('taxation_hotel_config', sa.Column('state_identifier', sa.Integer(), server_default='1', nullable=True))
    op.alter_column('taxation_hotel_config', 'hotel_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('taxation_hotel_config', 'hx_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True)
    op.create_unique_constraint(None, 'taxation_hotel_config', ['cs_hotel_id'])
    op.create_foreign_key(None, 'taxation_hotel_config', 'taxation_state', ['state_identifier'], ['id'], onupdate='CASCADE')
    op.add_column('taxation_service', sa.Column('category_id', sa.String(), nullable=True))
    op.add_column('taxation_service', sa.Column('declared_tariff_required', sa.Boolean(), nullable=True))
    op.create_unique_constraint(None, 'taxation_service', ['category_id'])
    op.add_column('taxation_sgst', sa.Column('service_id', sa.Integer(), server_default='1', nullable=False))
    op.add_column('taxation_sgst', sa.Column('state_identifier', sa.Integer(), server_default='1', nullable=False))
    op.create_foreign_key(None, 'taxation_sgst', 'taxation_service', ['service_id'], ['id'], onupdate='CASCADE')
    op.create_foreign_key(None, 'taxation_sgst', 'taxation_state', ['state_identifier'], ['id'], onupdate='CASCADE')
    op.add_column('taxation_state', sa.Column('cs_state_id', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'taxation_state', ['cs_state_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'taxation_state', type_='unique')
    op.drop_column('taxation_state', 'cs_state_id')
    op.drop_constraint(None, 'taxation_sgst', type_='foreignkey')
    op.drop_constraint(None, 'taxation_sgst', type_='foreignkey')
    op.drop_column('taxation_sgst', 'state_identifier')
    op.drop_column('taxation_sgst', 'service_id')
    op.drop_constraint(None, 'taxation_service', type_='unique')
    op.drop_column('taxation_service', 'declared_tariff_required')
    op.drop_column('taxation_service', 'category_id')
    op.drop_constraint(None, 'taxation_hotel_config', type_='foreignkey')
    op.drop_constraint(None, 'taxation_hotel_config', type_='unique')
    op.alter_column('taxation_hotel_config', 'hx_id',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('taxation_hotel_config', 'hotel_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('taxation_hotel_config', 'state_identifier')
    op.drop_column('taxation_hotel_config', 'cs_hotel_id')
    op.drop_constraint(None, 'taxation_declared_tariff', type_='foreignkey')
    op.alter_column('taxation_declared_tariff', 'modified_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('taxation_declared_tariff', 'hotel_identifier')
    op.drop_constraint(None, 'taxation_cgst', type_='foreignkey')
    op.drop_column('taxation_cgst', 'service_id')
    op.drop_index(op.f('ix_taxation_room_config_room_code'), table_name='taxation_room_config')
    op.drop_index(op.f('ix_taxation_room_config_name'), table_name='taxation_room_config')
    op.drop_table('taxation_room_config')
    # ### end Alembic commands ###
