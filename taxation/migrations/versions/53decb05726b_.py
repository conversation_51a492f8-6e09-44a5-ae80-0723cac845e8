"""empty message

Revision ID: 53decb05726b
Revises: 6ba0a9fc6efa
Create Date: 2018-02-26 19:46:04.374463

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '53decb05726b'
down_revision = '6ba0a9fc6efa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('taxation_hotel_config_hotel_name_key', 'taxation_hotel_config', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('taxation_hotel_config_hotel_name_key', 'taxation_hotel_config', ['hotel_name'])
    # ### end Alembic commands ###
