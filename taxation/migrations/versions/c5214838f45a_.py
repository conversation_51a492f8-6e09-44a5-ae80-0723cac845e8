"""empty message

Revision ID: c5214838f45a
Revises: 1d6629f51470
Create Date: 2019-05-29 13:01:10.958120

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c5214838f45a'
down_revision = '1d6629f51470'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_other_tax', sa.Column('tax_type_name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('taxation_other_tax', 'tax_type_name')
    # ### end Alembic commands ###
