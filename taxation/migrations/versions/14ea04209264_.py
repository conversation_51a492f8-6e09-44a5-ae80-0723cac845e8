"""empty message

Revision ID: 14ea04209264
Revises: 86b14a0a328e
Create Date: 2020-07-03 13:08:59.880997

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '14ea04209264'
down_revision = '86b14a0a328e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_tax',
                    sa.Column('tax_id', sa.Integer(), nullable=False),
                    sa.Column('tax_code', sa.String(), nullable=True),
                    sa.Column('description', sa.String(), nullable=True),
                    sa.Column('unit', sa.String(), nullable=True),
                    sa.Column('name', sa.String(), nullable=True),
                    sa.PrimaryKeyConstraint('tax_id'),
                    sa.UniqueConstraint('tax_code')
                    )
    op.create_table('taxation_tax_config',
                    sa.Column('tax_config_id', sa.Integer(), nullable=False),
                    sa.Column('tax_code', sa.String(), nullable=True),
                    sa.Column('service_category_id', sa.String(), nullable=True),
                    sa.Column('tax_type', sa.String(), nullable=False),
                    sa.Column('date_range', postgresql.DATERANGE(), nullable=False),
                    sa.Column('pretax_range', postgresql.NUMRANGE(), nullable=True),
                    sa.Column('posttax_range', postgresql.NUMRANGE(), nullable=True),
                    sa.Column('tax_value', sa.DECIMAL(), nullable=False),
                    sa.Column('included_in_rate', sa.Boolean(), nullable=True),
                    sa.Column('weekdays_byte', sa.Integer(), nullable=False),
                    sa.ForeignKeyConstraint(['service_category_id'], ['taxation_service.category_id'], ),
                    sa.ForeignKeyConstraint(['tax_code'], ['taxation_tax.tax_code'], ),
                    sa.PrimaryKeyConstraint('tax_config_id')
                    )
    op.create_table('taxation_hotel_tax_config_mapping',
                    sa.Column('hotel_id', sa.String(), nullable=False),
                    sa.Column('tax_config_id', sa.Integer(), nullable=False),
                    sa.ForeignKeyConstraint(['tax_config_id'], ['taxation_tax_config.tax_config_id'], ),
                    sa.ForeignKeyConstraint(['hotel_id'], ['taxation_hotel_config.cs_hotel_id'], ),
                    sa.PrimaryKeyConstraint('hotel_id', 'tax_config_id')
                    )

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('taxation_hotel_tax_config_mapping')
    op.drop_table('taxation_tax_config')
    op.drop_table('taxation_tax')
    # ### end Alembic commands ###
