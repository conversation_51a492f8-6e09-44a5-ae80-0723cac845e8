"""empty message

Revision ID: 7590e8f07425
Revises: 0169b7c6433d
Create Date: 2018-04-25 20:39:40.235171

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7590e8f07425'
down_revision = '0169b7c6433d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('taxation_sku')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_sku',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('hotel_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('room_type_code', postgresql.ENUM('acacia', 'oak', 'maple', 'mahogany', name='sku_room_codes'), autoincrement=False, nullable=True),
    sa.Column('occupancy', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['hotel_id'], ['taxation_hotel_config.hotel_id'], name='taxation_sku_hotel_id_fkey', onupdate='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='taxation_sku_pkey')
    )
    # ### end Alembic commands ###
