"""empty message

Revision ID: d0e786dc5c1a
Revises: f38a281064a8
Create Date: 2017-09-12 21:57:41.772674

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd0e786dc5c1a'
down_revision = 'f38a281064a8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_sku',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('hotel_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('room_type_code', sa.Enum('acacia', 'oak', 'maple', 'mahogany', name='sku_room_codes'), nullable=True),
    sa.Column('occupancy', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['hotel_id'], ['taxation_hotel_config.hotel_id'], onupdate='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('taxation_sku')
    # ### end Alembic commands ###
