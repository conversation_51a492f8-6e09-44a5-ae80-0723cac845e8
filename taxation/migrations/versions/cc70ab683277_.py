"""empty message

Revision ID: cc70ab683277
Revises: 7590e8f07425
Create Date: 2018-04-30 11:27:13.875093

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cc70ab683277'
down_revision = '7590e8f07425'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_declared_tariff', sa.Column('cs_hotel_id', sa.String(), server_default='', nullable=True))
    op.create_index(op.f('ix_taxation_declared_tariff_cs_hotel_id'), 'taxation_declared_tariff', ['cs_hotel_id'], unique=False)
    op.drop_constraint('taxation_declared_tariff_hotel_id_fkey', 'taxation_declared_tariff', type_='foreignkey')
    op.drop_constraint('taxation_hotel_config_state_id_fkey', 'taxation_hotel_config', type_='foreignkey')
    op.alter_column('taxation_state', 'state_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('taxation_state', 'state_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.create_foreign_key('taxation_hotel_config_state_id_fkey', 'taxation_hotel_config', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    op.create_foreign_key('taxation_declared_tariff_hotel_id_fkey', 'taxation_declared_tariff', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'], onupdate='CASCADE')
    op.drop_index(op.f('ix_taxation_declared_tariff_cs_hotel_id'), table_name='taxation_declared_tariff')
    op.drop_column('taxation_declared_tariff', 'cs_hotel_id')
    # ### end Alembic commands ###
