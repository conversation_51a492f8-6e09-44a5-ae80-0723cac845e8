"""empty message

Revision ID: 3bd31ef6718e
Revises: 53decb05726b
Create Date: 2018-03-09 17:48:14.170316

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3bd31ef6718e'
down_revision = '53decb05726b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('taxation_dt_packet', sa.Column('payload', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('taxation_dt_packet', 'payload')
    # ### end Alembic commands ###
