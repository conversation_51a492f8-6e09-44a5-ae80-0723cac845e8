"""empty message

Revision ID: ad9273ff3790
Revises: aa02782a7b40
Create Date: 2018-05-10 14:02:24.387559

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ad9273ff3790'
down_revision = 'aa02782a7b40'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('_cs_hotel_id_and_room_code_uc', 'taxation_room_config', ['cs_hotel_id', 'room_code'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('_cs_hotel_id_and_room_code_uc', 'taxation_room_config', type_='unique')
    # ### end Alembic commands ###
