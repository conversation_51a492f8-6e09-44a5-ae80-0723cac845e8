"""empty message

Revision ID: 4db240201cdc
Revises: 0e33760affc8
Create Date: 2017-05-04 22:53:07.997763

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4db240201cdc'
down_revision = '0e33760affc8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('taxation_arr_luxurytax_hotel_id_fkey', 'taxation_arr_luxurytax', type_='foreignkey')
    op.create_foreign_key(None, 'taxation_arr_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'], onupdate='CASCADE')
    op.add_column('taxation_hotel_config', sa.Column('state_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'taxation_hotel_config', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    op.drop_constraint('taxation_hotel_luxurytax_hotel_id_fkey', 'taxation_hotel_luxurytax', type_='foreignkey')
    op.create_foreign_key(None, 'taxation_hotel_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'], onupdate='CASCADE')
    op.create_foreign_key(None, 'taxation_state_luxurytax', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    op.create_foreign_key(None, 'taxation_statetax', 'taxation_state', ['state_id'], ['state_id'], onupdate='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'taxation_statetax', type_='foreignkey')
    op.drop_constraint(None, 'taxation_state_luxurytax', type_='foreignkey')
    op.drop_constraint(None, 'taxation_hotel_luxurytax', type_='foreignkey')
    op.create_foreign_key('taxation_hotel_luxurytax_hotel_id_fkey', 'taxation_hotel_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'])
    op.drop_constraint(None, 'taxation_hotel_config', type_='foreignkey')
    op.drop_column('taxation_hotel_config', 'state_id')
    op.drop_constraint(None, 'taxation_arr_luxurytax', type_='foreignkey')
    op.create_foreign_key('taxation_arr_luxurytax_hotel_id_fkey', 'taxation_arr_luxurytax', 'taxation_hotel_config', ['hotel_id'], ['hotel_id'])
    # ### end Alembic commands ###
