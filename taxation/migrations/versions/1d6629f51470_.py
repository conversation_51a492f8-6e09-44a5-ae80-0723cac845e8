"""empty message

Revision ID: 1d6629f51470
Revises: f13a4a906757
Create Date: 2019-05-28 18:10:37.081005

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1d6629f51470'
down_revision = 'f13a4a906757'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_tax_type',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('taxation_other_tax',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('service_name', sa.String(), nullable=False),
    sa.Column('service_id', sa.Integer(), server_default='1', nullable=False),
    sa.Column('state_id', sa.Integer(), nullable=False),
    sa.Column('state_identifier', sa.Integer(), server_default='1', nullable=False),
    sa.Column('from_price_pretax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price_pretax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('from_price_posttax', sa.DECIMAL(precision=19, scale=4), nullable=True),
    sa.Column('to_price_posttax', sa.DECIMAL(precision=19, scale=4), nullable=True),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('sgst_percent', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('tax_type_id', sa.Integer(), server_default='1', nullable=False),
    sa.ForeignKeyConstraint(['service_id'], ['taxation_service.id'], onupdate='CASCADE'),
    sa.ForeignKeyConstraint(['state_identifier'], ['taxation_state.id'], onupdate='CASCADE'),
    sa.ForeignKeyConstraint(['tax_type_id'], ['taxation_tax_type.id'], onupdate='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('taxation_other_tax')
    op.drop_table('taxation_tax_type')
    # ### end Alembic commands ###
