"""empty message

Revision ID: 3aa630cfe0e5
Revises: 4db240201cdc
Create Date: 2017-06-15 11:19:47.459653

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3aa630cfe0e5'
down_revision = '4db240201cdc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_service',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('taxation_cgst',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('service_name', sa.String(), nullable=False),
    sa.Column('from_price_pretax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price_pretax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('from_price_posttax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price_posttax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('cgst_percent', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.ForeignKeyConstraint(['service_name'], ['taxation_service.name'], onupdate='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('taxation_sgst',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('service_name', sa.String(), nullable=False),
    sa.Column('state_id', sa.Integer(), nullable=False),
    sa.Column('from_price_pretax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price_pretax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('from_price_posttax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('to_price_posttax', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('expiry_date', sa.Date(), nullable=False),
    sa.Column('sgst_percent', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.Column('gst_percent', sa.DECIMAL(precision=19, scale=4), nullable=False),
    sa.ForeignKeyConstraint(['service_name'], ['taxation_service.name'], onupdate='CASCADE'),
    sa.ForeignKeyConstraint(['state_id'], ['taxation_state.state_id'], onupdate='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('taxation_sgst')
    op.drop_table('taxation_cgst')
    op.drop_table('taxation_service')
    # ### end Alembic commands ###
