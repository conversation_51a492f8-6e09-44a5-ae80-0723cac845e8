"""empty message

Revision ID: 6ba0a9fc6efa
Revises: 7091e915a502
Create Date: 2018-02-06 23:45:20.484041

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6ba0a9fc6efa'
down_revision = '7091e915a502'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('taxation_global_config',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('modified_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('config_data_type', sa.Enum('text', 'int', 'float', 'boolean', 'date', 'time', 'timestamp', name='config_data_type'), nullable=False),
    sa.Column('config_name', sa.String(), nullable=False),
    sa.Column('config_value', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('active', 'inactive', 'deleted', name='status_type'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_taxation_global_config_config_name'), 'taxation_global_config', ['config_name'], unique=False)
    op.create_index(op.f('ix_taxation_global_config_created_at'), 'taxation_global_config', ['created_at'], unique=False)
    op.create_index(op.f('ix_taxation_global_config_modified_at'), 'taxation_global_config', ['modified_at'], unique=False)
    op.create_index(op.f('ix_taxation_global_config_status'), 'taxation_global_config', ['status'], unique=False)
    op.create_index('taxation_global_config_index', 'taxation_global_config', ['config_name', 'modified_at', 'created_at', 'status'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('taxation_global_config_index', table_name='taxation_global_config')
    op.drop_index(op.f('ix_taxation_global_config_status'), table_name='taxation_global_config')
    op.drop_index(op.f('ix_taxation_global_config_modified_at'), table_name='taxation_global_config')
    op.drop_index(op.f('ix_taxation_global_config_created_at'), table_name='taxation_global_config')
    op.drop_index(op.f('ix_taxation_global_config_config_name'), table_name='taxation_global_config')
    op.drop_table('taxation_global_config')
    # ### end Alembic commands ###
