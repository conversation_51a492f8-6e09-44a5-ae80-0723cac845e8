"""empty message

Revision ID: 55ca29d1962e
Revises: 3aa630cfe0e5
Create Date: 2017-06-15 17:26:51.736692

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '55ca29d1962e'
down_revision = '3aa630cfe0e5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('taxation_cgst', 'from_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=True)
    op.alter_column('taxation_cgst', 'to_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=True)
    op.alter_column('taxation_sgst', 'from_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=True)
    op.alter_column('taxation_sgst', 'to_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('taxation_sgst', 'to_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=False)
    op.alter_column('taxation_sgst', 'from_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=False)
    op.alter_column('taxation_cgst', 'to_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=False)
    op.alter_column('taxation_cgst', 'from_price_posttax',
               existing_type=sa.NUMERIC(precision=19, scale=4),
               nullable=False)
    # ### end Alembic commands ###
