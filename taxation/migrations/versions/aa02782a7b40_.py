"""empty message

Revision ID: aa02782a7b40
Revises: cc70ab683277
Create Date: 2018-05-07 17:49:17.433906

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'aa02782a7b40'
down_revision = 'cc70ab683277'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('taxation_declared_tariff', 'hotel_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.add_column('taxation_room_config', sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False))
    op.add_column('taxation_room_config', sa.Column('modified_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('taxation_room_config', 'modified_at')
    op.drop_column('taxation_room_config', 'created_at')
    op.alter_column('taxation_declared_tariff', 'hotel_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
