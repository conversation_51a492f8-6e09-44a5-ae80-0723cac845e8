"""empty message

Revision ID: 04fe62ff1200
Revises: 0f28cf2d3864
Create Date: 2020-07-17 16:07:24.399491

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '04fe62ff1200'
down_revision = '0f28cf2d3864'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_tax_config_tax_code_service_category', 'taxation_tax_config', ['tax_code', 'service_category_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_tax_config_tax_code_service_category', table_name='taxation_tax_config')
    # ### end Alembic commands ###
