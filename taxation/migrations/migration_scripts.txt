taxation_state

UPDATE taxation_state
    SET cs_state_id = (CASE state_name
        WHEN 'Karnataka' THEN '1'
        WHEN 'Gujarat' THEN '2'
        WHEN 'Punjab' THEN '3'
        WHEN 'Telangana' THEN '4'
        WHEN 'Haryana' THEN '5'
        WHEN 'Madhya Pradesh' THEN '6'
        WHEN 'Maharashtra' THEN '7'
        WHEN 'Uttarakhand' THEN '8'
        WHEN 'Kerala' THEN '9'
        WHEN 'Himachal Pradesh' THEN '10'
        WHEN 'New Delhi' THEN '11'
        WHEN 'Uttar Pradesh' THEN '12'
        WHEN 'Andhra Pradesh' THEN '13'
        WHEN 'Tamil Nadu' THEN '14'
        WHEN 'Pondicherry' THEN '15'
        WHEN 'Rajasthan' THEN '16'
        WHEN 'Goa' THEN '17'
        WHEN 'West Bengal' THEN '18'
        WHEN 'Jharkhand' THEN '19'
        WHEN 'Assam' THEN '20'
        WHEN 'Odisha' THEN '21'
        WHEN 'Chattisgarh' THEN '22'
        WHEN 'Sikkim' THEN '23'
        WHEN 'Jammu and Kashmir' THEN '24'
    END);

taxation_cgst
update taxation_cgst set service_id=taxation_service.id from taxation_service  where taxation_cgst.service_name=taxation_service."name";

taxation_sgst
update taxation_sgst set service_id=taxation_service.id from taxation_service  where taxation_sgst.service_name=taxation_service."name";
update taxation_sgst set state_identifier=taxation_state.id from taxation_state  where taxation_sgst.state_id=taxation_state.state_id;

taxation_hotel_config
update taxation_hotel_config set state_identifier=taxation_state.id from taxation_state  where taxation_hotel_config.state_id=taxation_state.state_id;

taxation_declared_tariff
update taxation_declared_tariff set hotel_identifier=taxation_hotel_config.id from taxation_hotel_config  where taxation_declared_tariff.hotel_id=taxation_hotel_config.hotel_id;
update taxation_declared_tariff set cs_hotel_id=taxation_hotel_config.cs_hotel_id from taxation_hotel_config  where taxation_declared_tariff.hotel_id=taxation_hotel_config.hotel_id;

