import logging

from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from taxation.consumer.base_rmq_consumer import BaseRMQConsumer
from taxation.consumer.consumer_config import CatalogConfig
from taxation.decorators import consumer_middleware
from taxation.domain.tax.dto.cs_category_sync_dto import CSCategorySyncDTO
from taxation.domain.tax.dto.property_sync_dto import PropertySyncDto
from taxation.domain.tax.dto.seller_dto import SellerDto
from taxation.domain.tax.services import ConfigManagementService

logger = logging.getLogger(__name__)


class CatalogServiceConsumer(BaseRMQConsumer):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(CatalogConfig(tenant_id=tenant_id))
        logger.info("Listening to RMQ on host: %s from queue: %s", self.connection, self.queue)

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        obj = body
        logger.info("Catalog process_message called for entity: %s", obj.get('entity'))

        try:
            if obj.get('entity') == "PROPERTY":
                self.process_property_message(obj)

            if obj.get('entity') == 'SELLER':
                self.process_seller_message(obj)

            if obj.get('entity') == 'SKU_CATEGORY':
                self.process_hsn_message(obj)

        except Exception as exc:
            logger.exception("Unable to process catalog message")
            message.reject()
            return

        logger.info("Catalog message process complete. Message acknowledged")
        message.ack()

    @staticmethod
    def process_property_message(event_dict):
        property_sync_dto = PropertySyncDto(event_dict['hx_id'], event_dict['cs_property_id'],
                                            event_dict['data']['location']['state']['id'],
                                            event_dict['data']['location']['state']['name'],
                                            event_dict['data']['status'],
                                            event_dict['data']['name']['new_name'],
                                            sku_configs=event_dict['data']['skus'],
                                            room_type_configs=event_dict['data']['room_type_configs'])
        ConfigManagementService.hotel_sync_from_cs(property_sync_dto)
        ConfigManagementService.create_tax_config_as_property_onboards(property_sync_dto.cs_hotel_id,
                                                                       property_sync_dto.state_id)

    @staticmethod
    def process_seller_message(obj):
        seller_details_dto = SellerDto.create_from_catalog_data(obj.get('data'))
        ConfigManagementService.seller_sync_from_cs(seller_details_dto)

    @staticmethod
    def process_hsn_message(obj):
        cs_category_sync_dto = CSCategorySyncDTO(obj['data'])
        ConfigManagementService.service_sync_from_cs(cs_category_sync_dto)
