import logging

from flask import current_app

from taxation.alerts import slack_alert
from taxation.consumer.base_consumer import BaseConsumer
from taxation.domain.tax.dto.cs_category_sync_dto import CSCategorySyncDTO
from taxation.domain.tax.dto.property_room_sync_dto import PropertyRoomSyncDto
from taxation.domain.tax.dto.property_sync_dto import PropertySyncDto
from taxation.domain.tax.schema import CSPropertySchema, CSPropertyRoomSchema, CSCategorySchema
from taxation.domain.tax.services import ConfigManagementService
from taxation.utils.cleanup_db_session import CleanupSession

logger = logging.getLogger('consumer.' + __name__)


class CSHotelSyncConsumer(BaseConsumer):
    """
    Documentation link: https://treebo.atlassian.net/wiki/spaces/EN/pages/********/Exchange+and+Message+Structure
    event: Property Message (routing_key = com.cs.property)
    """

    def __init__(self):
        try:
            consumer_name = 'cataloging_service_hotel_sync'
            queue_arguments = {
                'x-dead-letter-exchange': current_app.config.get('RMQ_PUBLISHER')['dead_letter']['RMQ_EXCHANGE']
            }
            super(CSHotelSyncConsumer, self).__init__(consumer_name, queue_arguments)
        except Exception as ex:
            slack_alert(message="CSHotelSyncConsumer initialization error, %s" % str(ex))
            logger.exception(ex)

    def on_message(self, event_dict, message):
        try:
            with CleanupSession():
                _, error = CSPropertySchema().load(event_dict)
                if error:
                    logger.error("CSHotelSyncConsumer data format error,Data:%s, Error:%s" % (event_dict, error))
                    slack_alert(data=event_dict, message="CSHotelSyncConsumer data format error",
                                channel='consumer')
                    message.reject()
                    return
                if not event_dict['hx_id']:
                    # in_place replacement for message
                    traceback_message = "CSHotelSyncConsumer data format error,Hx id missing."
                    logger.error("%s, data: %s" % (traceback_message, str(event_dict)))
                    slack_alert(message=traceback_message, channel='consumer')
                    message.reject()
                    return

                property_sync_dto = PropertySyncDto(event_dict['hx_id'], event_dict['cs_property_id'],
                                                    event_dict['data']['location']['state']['id'],
                                                    event_dict['data']['location']['state']['name'],
                                                    event_dict['data']['status'],
                                                    event_dict['data']['name']['new_name'],
                                                    sku_configs=event_dict['data']['skus'],
                                                    room_type_configs=event_dict['data']['room_type_configs'])
                ConfigManagementService.activate_inactive_sku(property_sync_dto)
                ConfigManagementService.hotel_sync_from_cs(property_sync_dto)
                message.ack()
        except Exception as ex:
            logger.exception("Exception(%s) occurred in CSHotelSyncConsumer. details: %s" % (repr(ex), event_dict))
            slack_alert(data=property_sync_dto,
                        message="Exception occurred in CSHotelSyncConsumer, error: %s" % repr(ex), channel='consumer')
            message.reject()


class CSRoomSyncConsumer(BaseConsumer):
    """
    Documentation link: https://treebo.atlassian.net/wiki/spaces/EN/pages/********/Exchange+and+Message+Structure
    event: Property-Room Message (routing_key = com.cs.property.room)
    Sample:
    {'data': {'size': None, 'room_number': 'H5', 'room_type': {'unirate_room_type_code': 'treebo-maple',
    'bb_room_type_code': 'Maple (Deluxe)', 'crs_room_type_code': 'MAPLE', 'id': 3, 'code': 'rt03', 'type': 'MAPLE'},
    'is_active': True, 'floor_number': '1', 'id': 56349, 'building_number': '1', 'room_size': 155.0}, 'hx_id': 12792,
    'entity': 'ROOM', 'cs_property_id': '1506446', 'operation_type': 'CREATE'}
    """

    def __init__(self):
        try:
            consumer_name = 'cataloging_service_room_sync'
            queue_arguments = {
                'x-dead-letter-exchange': current_app.config.get('RMQ_PUBLISHER')['dead_letter']['RMQ_EXCHANGE']
            }
            super(CSRoomSyncConsumer, self).__init__(consumer_name, queue_arguments)
        except Exception as ex:
            slack_alert(message="CSRoomSyncConsumer initialization error, %s" % str(ex))
            logger.exception(ex)

    def on_message(self, event_dict, message):
        try:
            with CleanupSession():
                _, error = CSPropertyRoomSchema().load(event_dict)
                if error:
                    logger.error("CSRoomSyncConsumer sync error,Data:%s, Error:%s" % (event_dict, error))
                    slack_alert(data=event_dict, message="CSRoomSyncConsumer data format error",
                                channel='consumer')
                    message.reject()
                    return
                # if not event_dict['hx_id']:
                #     # in_place replacement for message
                #     traceback_message = "CSRoomSyncConsumer data format error,Hx id missing."
                #     logger.error("%s, data: %s" % (traceback_message, str(event_dict)))
                #     slack_alert(message=traceback_message, channel='consumer')
                #     message.reject()
                #     return
                property_room_sync_dto = PropertyRoomSyncDto(event_dict['cs_property_id'],
                                                             event_dict['data']['room_type']['type'],
                                                             event_dict['data']['room_type']['code'])
                ConfigManagementService.room_sync_from_cs(property_room_sync_dto)
                message.ack()
        except Exception as ex:
            logger.exception(
                "Exception(%s) occurred in CSRoomSyncConsumer. details: %s" % (repr(ex), property_room_sync_dto))
            slack_alert(data=property_room_sync_dto, channel='consumer',
                        message="Exception occurred in CSRoomSyncConsumer, error: %s" % repr(ex))
            message.reject()


class CSServiceSyncConsumer(BaseConsumer):
    """
    Documentation link: https://treebo.atlassian.net/wiki/spaces/EN/pages/********/Exchange+and+Message+Structure
    event: Sku Category Message (routing_key = com.cs.sku.category)
    Sample:
    {'operation_type': 'UPDATE', 'data': {'status': 'ACTIVE', 'name': 'Alcohol', 'id': 8, 'code': 'alcohol',
    'hsn_sac': 996334}, 'entity': 'SKU_CATEGORY'}
    """

    def __init__(self):
        try:
            consumer_name = 'cataloging_service_category_sync'
            queue_arguments = {
                'x-dead-letter-exchange': current_app.config.get('RMQ_PUBLISHER')['dead_letter']['RMQ_EXCHANGE']
            }
            super(CSServiceSyncConsumer, self).__init__(consumer_name, queue_arguments)
        except Exception as ex:
            slack_alert(message="CSServiceSyncConsumer initialization error, %s" % str(ex))
            logger.exception(ex)

    def on_message(self, event_dict, message):

        try:
            with CleanupSession():
                _, error = CSCategorySchema().load(event_dict)
                if error:
                    logger.error("CSServiceSyncConsumer sync error, Data:%s, Error:%s" % (event_dict, error))
                    message.reject()
                    slack_alert(data=event_dict, message="CSServiceSyncConsumer data format error",
                                channel='consumer')
                    return
                cs_category_sync_dto = CSCategorySyncDTO(event_dict['data'])

                result = ConfigManagementService.service_sync_from_cs(cs_category_sync_dto)
                logger.debug("service sync success, Data: %s, Status: %s" % (event_dict, result))
                message.ack()
        except Exception as ex:
            logger.exception(
                "Exception(%s) occurred in CSServiceSyncConsumer. details: %s" % (repr(ex), cs_category_sync_dto))
            slack_alert(data=cs_category_sync_dto, channel='consumer',
                        message="Exception occurred in CSServiceSyncConsumer, error: %s" % repr(ex))
            message.reject()
