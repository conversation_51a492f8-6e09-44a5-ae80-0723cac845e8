import importlib
import logging

from flask import current_app
from kombu import Connection, Exchange, Queue
from kombu.mixins import ConsumerMixin

from taxation.alerts import slack_alert

logger = logging.getLogger(__name__)


class BaseConsumer(ConsumerMixin):
    def __init__(self, consumer_name, queue_arguments=None):
        self.retry_count = 0
        broker_url, exchange_name, exchange_type, queue_name, queue_routing_key = self.fetch_connection_params(
            consumer_name)
        with Connection(broker_url, heartbeat=5) as conn:
            exchange = Exchange(exchange_name, exchange_type)
            self.queue = Queue(queue_name, exchange, routing_key=queue_routing_key, queue_arguments=queue_arguments)
            self.connection = conn
        logger.info(
            "Consumer(%s), Url: %s, exchange: %s, queue_name: %s, routing_key: %s, exchange_type: %s"
            % (consumer_name, broker_url, exchange_name, queue_name, queue_routing_key, exchange_type))

    def start_consumer(self):
        retry_count_limit = current_app.config.get('RMQ_RETRY_COUNT', 5)
        while True:
            try:
                self.run()
            except KeyboardInterrupt:
                logger.exception("Exiting the consumer process")
                break
            except Exception as ex:
                logger.exception(ex)
                self.retry_count += 1
                if self.retry_count >= retry_count_limit:
                    queue = self.queue
                    exchange = self.queue.exchange
                    consumer_name = self.__class__.__name__
                    queue_related_details = "Queue Name: %s, Routing key: %s, Exchange Name: %s, Exchange Type: %s" % (
                        queue.name, queue.routing_key, exchange.name, exchange.type)
                    message = "Consumer(Name: %s) not started, details: %s" % (consumer_name, queue_related_details)
                    slack_alert(message=message)
                    break
                self.start_consumer()

    def handle_message(self, body: dict, message):
        try:
            self.on_message(body, message)
        except Exception as ex:
            slack_alert(data=body, message="Exception occurred in BaseConsumer, message: %s" % str(ex),
                        channel='consumer')
            logger.exception(ex)
            message.ack()

    def on_message(self, event_obj, message):
        raise NotImplementedError("Base Consumer: on_message, Cannot be implemented")

    def get_consumers(self, consumer, channel):
        return [consumer(self.queue, callbacks=[self.handle_message],
                         prefetch_count=current_app.config.get('RMQ_PREFETCH_COUNT'))]

    @staticmethod
    def fetch_connection_params(external_system):
        if not external_system:
            raise Exception("Not a Valid External System from Config")
        rmq_dict = current_app.config.get('RMQ_CONSUMER_CONFIGS')[external_system]
        broker_url = rmq_dict['url']
        exchange_name = rmq_dict['exchange_name']
        exchange_type = rmq_dict['exchange_type']
        queue_name = rmq_dict['queue_name']
        queue_routing_key = rmq_dict['queue_routing_key']
        return broker_url, exchange_name, exchange_type, queue_name, queue_routing_key


def start_consumer(**options):
    consumer = options['consumer_name']
    if not consumer:
        logger.error("No Listeners to start. Configure list of consumer classes & start again")
        raise Exception("No Listeners to start. Configure list of consumer classes & start again")
    module_name, class_name = consumer.rsplit(".", 1)
    logger.info("Starting logger: module  %s , class %s ", module_name, class_name)
    _cls = getattr(importlib.import_module(module_name), class_name)
    if _cls:
        _cls().start_consumer()
