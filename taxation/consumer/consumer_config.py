from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient


class CatalogConfig(object):
    """
    Catalog Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = 'cs_exchange'
        self.exchange_type = 'topic'
        self.queue_name = 'catalog-event-queue'
        self.routing_keys = ['com.cs.property',
                             'com.cs.sku.category',
                             'com.cs.seller']
        self.exclusive = False
