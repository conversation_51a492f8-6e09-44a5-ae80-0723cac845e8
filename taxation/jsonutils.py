import datetime
import decimal
import json

date_keys = {'date'}
price_keys = {"price", "tax", "pretax", "posttax", "percent", "room_rate"}


def date_hook(json_dict):
    count = 0
    strptime = datetime.datetime.strptime
    for (key, value) in json_dict.items():
        if any(date_key in key for date_key in date_keys):
            try:
                json_dict[key] = strptime(value, "%Y-%m-%d").date()
            except:
                json_dict[key] = value
        elif any(price_key in key for price_key in price_keys):
            try:
                json_dict[key] = decimal.Decimal(str(value))
            except:
                json_dict[key] = value
    return json_dict


class DateTimeEncoder(json.JSONEncoder):
    def default(self, o):  # pylint: disable=E0202
        if isinstance(o, datetime.datetime) or isinstance(o, datetime.date):
            return o.isoformat()
        elif isinstance(o, decimal.Decimal):
            return str(o)
        return super().default(o)
