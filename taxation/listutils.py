from collections import defaultdict


def convert_to_dict(source_list, attr):
    target_dict = {getattr(item, attr): item for item in source_list}
    return target_dict


def convert_to_multivalue_dict(source_list, key):
    target_dict = defaultdict(list)
    for item in source_list:
        target_dict[key(item)].append(item)
    return target_dict


def value_in_range(value, start, end):
    return start <= value <= end


def group_list(values, *keys, nesting_level=1):
    if nesting_level == 1:
        grouped_values = defaultdict(list)
    elif nesting_level == 2:
        grouped_values = defaultdict(lambda: defaultdict(list))
    elif nesting_level == 3:
        grouped_values = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
    else:
        raise ValueError("Max 3 attributes supported")

    for value in values:
        if nesting_level == 2:
            grouped_values[keys[0](value)][keys[1](value)].append(value)
        elif nesting_level == 3:
            grouped_values[keys[0](value)][keys[1](value)][keys[2](value)].append(value)
        else:
            grouped_values[keys[0](value)].append(value)
    return grouped_values
