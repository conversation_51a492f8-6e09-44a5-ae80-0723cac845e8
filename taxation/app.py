import logging
import os

import click
from flask import Flask
from flask_admin import Admin
from healthcheck import HealthCheck
from healthcheck.healthcheck import rds_available
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.flask.after_request import clear_request_context

from taxation import admin_views
from taxation import commands as cmds
from taxation.api.v1.cache_api import bp as tax_cache_bp
from taxation.api.v1.tax_api import bp as tax_bp
from taxation.api.v3.tax_api import bp as tax_v3_bp
from taxation.api.v4.tax_api import bp as tax_v4_bp
from taxation.api.v5.tax_api import bp as tax_v5_bp
from taxation.api.v6.tax_api import bp as tax_v6_bp
from taxation.api_docs import init_docs
from taxation.infrastructure.cached_rmq_url import get_rmq_url

logger = logging.getLogger(__name__)

config = {
    "development": "taxation.settings.DevelopmentConfig",
    "staging": "taxation.settings.StagingConfig",
    "testing": "taxation.settings.TestingConfig",
    "default": "taxation.settings.DevelopmentConfig",
    "production": "taxation.settings.ProductionConfig",
    "performance": "taxation.settings.PerformanceConfig",
}

app = Flask(__name__, instance_relative_config=True,
            instance_path=os.environ.get('FLASK_APP_INSTANCE_PATH'))


def configure_swagger(app):
    app.config['SWAGGER'] = {
        'title': 'taxation',
        'uiversion': 3,
    }


def create_app():
    config_app(app)
    register_blueprint(app)
    setup_admin(app)
    app.before_request_funcs = {None: app.config['BEFORE_REQUEST_MIDDLEWARES']}
    app.after_request_funcs = {None: app.config['AFTER_REQUEST_MIDDLEWARES']}
    health_check(app)
    register_commands(app)
    configure_swagger(app)
    init_docs(app)

    @app.teardown_request
    def shutdown_session(exception=None):
        db_engine.remove_session()

    @app.teardown_appcontext
    def clear_thread_local(exception=None):
        try:
            logger.info("Connection pool status: %s", db_engine.get_engine().pool.status())
        except:
            logger.error("Can't log connection pool status")
            pass
        clear_request_context()

    return app


def health_check(app):
    """

    :param app:
    :return:
    """
    health = HealthCheck(app, '/tax/api/health', ['rds'])

    def rds_available():
        for tenant_id, scoped_session in db_engine.tenant_wise_sessions.items():
            Session = scoped_session()
            try:
                logger.info("Making connection with RDS for tenant_id {0}".format(tenant_id))
                Session.execute('SELECT 1')
                logger.info("Connection successful with RDS")
            except Exception as e:
                logger.error('Exception occured while connection with RDS %s' % e)
                raise e
            finally:
                scoped_session.remove()
        return True, "connection successful"

    def rabbitmq_available():
        from kombu import Connection
        from amqp.exceptions import ConnectionError
        try:
            for tenant in TenantClient.get_active_tenants():
                tenant_id = tenant.tenant_id
                rmq_url = get_rmq_url(tenant_id)
                conn = Connection(rmq_url, transport_options={'confirm_publish': True})
                try:
                    conn.connect()
                    if not conn.connected:
                        logger.error('Connection with rabbitmq failed. TenantId: %s, RMQ URL: %s', tenant_id,
                                     rmq_url)
                        raise ConnectionError()
                except Exception as e:
                    logger.error('Connection with rabbitmq failed. TenantId: %s, RMQ URL: %s', tenant_id, rmq_url)
                    raise

                conn.release()
                logger.info("Connection successful with Rabbitmq for tenant_id: %s", tenant.tenant_id)
        except Exception as e:
            raise ConnectionError()

        return True, "connection successful"

    health.add_check(rds_available)
    health.add_check(rabbitmq_available)


def setup_admin(app):
    admin = Admin(app, name='Taxation', template_mode='bootstrap3', url='/tax/admin')
    tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())
    for admin_view in admin_views.__all__:
        model_view = getattr(admin_views, admin_view)
        admin.add_view(model_view(model_view._model, db_engine.get_scoped_session(tenant_id=tenant_id)))


def config_app(app):
    environment = os.environ.get('APP_ENV', 'development')
    click.echo("Using environment: %s" % environment)
    app.config.from_object(config[environment])
    app.config.from_pyfile('taxation.cfg', silent=True)
    if not os.environ.get('APP_ENV', 'testing'):
        configure_logging(app)


def configure_logging(app):
    import logging.config
    environment = os.environ.get('APP_ENV', 'development')
    log_level = "INFO" if environment in ["production"] else "DEBUG"

    LOGGING = {
        'version': 1,
        'filters': {
            'request_id': {
                '()': 'treebo_commons.request_tracing.log_filters.RequestContextFilter',
            },
        },
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': "[%(asctime)s] %(levelname)s %(request_id)s [%(name)s:%(lineno)s] %(message)s",
                'datefmt': "%Y-%m-%d %H:%M:%S"
            },
            'logstash': {
                '()': 'logstash_formatter.LogstashFormatterV1'
            }
        },
        'handlers': {
            'null': {
                'level': 'INFO',
                'class': 'logging.NullHandler',
                'filters': ['request_id'],
            },
            'console': {
                'level': 'INFO',
                'class': 'logging.StreamHandler',
                'formatter': 'logstash',
                'filters': ['request_id'],
            },
        },
        'loggers': {
            'taxation': {
                'handlers': ['console'],
                'level': log_level,
                'propagate': False,
            },
            'declared_tariff': {
                'handlers': ['console'],
                'level': log_level,
                'propagate': False,
            },
            'request_handler': {
                'handlers': ['console'],
                'level': log_level,
                'propagate': False,
            },
            'consumer': {
                'handlers': ['console'],
                'level': log_level,
                'propagate': False,
            },
            'error': {
                'handlers': ['console'],
                'level': log_level,
                'propagate': False,
            },
            'treebo_commons': {
                'handlers': ['console'],
                'level': 'INFO',
            },
            '': {
                'handlers': ['console'],
                'level': log_level,
            },
        },
    }
    logging.config.dictConfig(LOGGING)


def register_blueprint(app):
    app.register_blueprint(tax_bp, url_prefix="/tax/v1")
    app.register_blueprint(tax_cache_bp, url_prefix="/tax/v1/cache")
    app.register_blueprint(tax_v3_bp, url_prefix='/tax/v3')
    app.register_blueprint(tax_v4_bp, url_prefix='/tax/v4')
    app.register_blueprint(tax_v5_bp, url_prefix='/tax/v5')
    app.register_blueprint(tax_v6_bp, url_prefix='/tax/v6')


def register_commands(app):
    app.cli.add_command(cmds.start_catalog_consumer)
    app.cli.add_command(cmds.migrate_tax_model)
