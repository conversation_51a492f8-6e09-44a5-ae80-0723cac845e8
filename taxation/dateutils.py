import pytz
from collections import namedtuple
from datetime import datetime, timedelta

DateRange = namedtuple('DateRange', ['start', 'end'])
local = pytz.timezone('Asia/Kolkata')
delta = timedelta(days=1)


def is_overlapping_date_range(date_range1, date_range2):  # noqa
    max_start = max(date_range1.start, date_range2.start)
    min_end = min(date_range1.end, date_range2.end)
    return (min_end - max_start).days + 1 > 0


def convert_naive_datetime_to_utc_timezone(naive_datetime):
    datetime_with_local_timezone = local.localize(naive_datetime)
    datetime_in_utc = datetime_with_local_timezone.astimezone(pytz.utc)
    return datetime_in_utc


def ymd_str_to_date(ymd_date_string):
    try:
        return datetime.strptime(ymd_date_string, '%Y-%m-%d').date()
    except:
        return ymd_date_string


def date_to_ymd_str(d):
    return datetime.strftime(d, '%Y-%m-%d')


def current_date_in_india_timezone():
    current_date = datetime.now(local).date()
    return current_date


def get_datetime_isoformat():
    return datetime.utcnow().replace(microsecond=0).isoformat()


def date_range(start_date, end_date):
    yield start_date
    current_date = start_date + delta
    while current_date <= end_date:
        yield current_date
        current_date = current_date + delta
