import logging

import flask
from flask import flash, url_for
from flask_admin.actions import action
from flask_admin.babel import gettext
from flask_admin.contrib.sqla import ModelView
from flask_admin.model import typefmt
from markupsafe import Markup
from psycopg2._range import DateRange, NumericRange, Range
from wtforms import validators, DecimalField, SelectField
from wtforms.fields.html5 import DateField
from wtforms.validators import Optional

from taxation import models
from taxation.domain.tax import repo_provider
from taxation.domain.tax.services import ConfigManagementService
from taxation.models.tax_model import TaxType

logger = logging.getLogger(__name__)

__all__ = ['StateView', 'HotelConfigModelView', 'ServiceView', 'CGSTView', 'SGSTView', 'OtherTaxView', 'TaxTypeView',
           'TaxView', 'TaxConfigView', 'HotelTaxConfigMappingView', 'SellerTaxConfigMappingView', 'SellerModelView',
           'HotelTaxableAmountRuleMappingView', 'TaxableAmountRuleView']


class Validators(object):
    def no_negatives(self, field):
        """
        :param self: form instance to which the validator is linked
        :param field: field from the admin form on which validation is to be run
        """
        if (not Validators.check_if_null(self, field)) and field.data < 0:
            raise validators.ValidationError('Field should be positive')
        return True

    def value_too_large(self, field):
        """
        :param self: form instance to which the validator is linked
        :param field: field from the admin form on which validation is to be run
        """
        if (not Validators.check_if_null(self, field)) and field.data >= 10000000:
            raise validators.ValidationError('Field value can\'t be so large, value has to be less than 10,000,000')
        return True

    def check_if_null(self, field):
        """
        :param self: form instance to which the validator is linked
        :param field: field from the admin form on which validation is to be run
        """
        if field.data is None:
            raise validators.ValidationError('Field should not be null/empty')
        return False


class HotelConfigModelView(ModelView):
    _model = models.HotelConfig
    column_list = ['id', 'hotel_id', 'hx_id', 'cs_hotel_id', 'hotel_name', 'state.state_name',
                   'declared_tariff_enabled', 'churned']
    column_sortable_list = column_list
    column_filters = ['hotel_id', 'hotel_name', 'hotel_luxury_tax_enabled',
                      'arr_tax_enabled', 'state.state_name', 'declared_tariff_enabled', 'hx_id', 'cs_hotel_id']
    column_labels = ({'state.state_name': 'State Name', 'hotel_name': 'Name', 'declared_tariff_enabled': 'DT-enabled'})

    form_args = dict(hotel_id=dict(validators=[Validators.no_negatives]))
    form_excluded_columns = ['state_id', 'arr_tax_enabled', 'use_arr_for_tax_calculation', 'hotel_luxury_tax_enabled']

    can_create = False
    can_delete = False

    def on_model_change(self, form, model, is_created):
        if model.state:
            model.state_id = model.state.state_id

    @action('activate_inactive_catalog_sku', 'Activate Skus', 'Are you sure you want to activate skus?')
    def activate_inactive_skus(self, ids):
        ConfigManagementService.activate_inactive_sku(ids)


class StateView(ModelView):
    _model = models.State
    column_list = ['id', 'state_id', 'state_name', 'cs_state_id']
    form_columns = column_list
    column_editable_list = form_columns
    form_args = dict(tax_value=dict(validators=[Validators.no_negatives]))

    can_create = False
    can_edit = False
    can_delete = False


class ServiceView(ModelView):
    _model = models.Service
    column_list = ['id', 'name', 'category_id', 'service_accounting_code', 'declared_tariff_required']

    can_create = False
    can_edit = True
    can_delete = False


class CGSTView(ModelView):
    _model = models.CGST
    column_list = ['id', 'service_name', 'from_price_pretax', 'to_price_pretax',
                   'effective_date', 'expiry_date', 'cgst_percent']
    column_filters = ['service_name', 'effective_date', 'expiry_date', 'cgst_percent']
    form_excluded_columns = ['service_name']

    def on_model_change(self, form, model, is_created):
        if model.cgst_percent > 100 or model.cgst_percent < 0:
            raise validators.ValidationError(
                'Tax Percentage cannot be greater than 100 and will be a non-negative value')
        if model.effective_date > model.expiry_date:
            raise validators.ValidationError('Effective Date should be less than Expiry Date')
        model.service_name = model.service.name


class SGSTView(ModelView):
    _model = models.SGST
    column_list = ['id', 'service_name', 'state.state_name', 'from_price_pretax', 'to_price_pretax',
                   'from_price_posttax', 'to_price_posttax',
                   'effective_date', 'expiry_date', 'sgst_percent', 'gst_percent']
    column_labels = ({'state.state_name': 'State Name'})
    form_excluded_columns = ['service_name', 'state_id']
    column_filters = ['service_name', 'state_id', 'state.state_name', 'effective_date', 'expiry_date',
                      'sgst_percent', 'gst_percent']
    column_descriptions = dict(
        gst_percent='GST percent = (SGST + CGST) percent, for the same price slab'
    )

    def on_model_change(self, form, model, is_created):
        if model.sgst_percent > 100 or model.sgst_percent < 0:
            raise validators.ValidationError(
                'Tax Percentage cannot be greater than 100 and will be a non-negative value')
        if model.gst_percent > 100 or model.gst_percent < 0:
            raise validators.ValidationError(
                'Tax Percentage cannot be greater than 100 and will be a non-negative value')
        if model.effective_date > model.expiry_date:
            raise validators.ValidationError('Effective Date should be less than Expiry Date')
        model.service_name = model.service.name
        model.state_id = model.state.state_id

        all_cgst = repo_provider.gst_repository.get_cgst_for_service(model.service.name)

        for cgst in all_cgst:
            if cgst.from_price_pretax == model.from_price_pretax and cgst.to_price_pretax == model.to_price_pretax:
                if model.gst_percent != model.sgst_percent + cgst.cgst_percent:
                    raise validators.ValidationError('GST percent should be sum of CGST + SGST percent for same slab. '
                                                     'CGST percent is: %s' % str(cgst.cgst_percent))


class TaxTypeView(ModelView):
    _model = models.TaxType


class OtherTaxView(ModelView):
    _model = models.OtherTax
    column_list = ['id', 'service_name', 'state.state_name', 'from_price_pretax', 'to_price_pretax',
                   'from_price_posttax', 'to_price_posttax', 'effective_date', 'expiry_date', 'tax_percent', 'tax_type']
    form_excluded_columns = ['state_id', 'service_name', 'tax_type_name']
    column_filters = ['service_name', 'state_id', 'state.state_name', 'effective_date', 'expiry_date',
                      'tax_percent', 'tax_type']
    column_labels = ({'state.state_name': 'State Name'})

    def on_model_change(self, form, model, is_created):
        if model.tax_percent > 100 or model.tax_percent < 0:
            raise validators.ValidationError(
                'Tax Percentage cannot be greater than 100 and will be a non-negative value')
        if model.effective_date > model.expiry_date:
            raise validators.ValidationError('Effective Date should be less than Expiry Date')
        model.service_name = model.service.name
        model.state_id = model.state.state_id
        model.tax_type_id = model.tax_type.id
        model.tax_type_name = model.tax_type.name


def enum_field_options(enum):
    """Produce WTForm Field instance configuration options for an Enum

    Returns a dictionary with 'choices' and 'coerce' keys, use this as
    **enum_fields_options(EnumClass) when constructing a field:

    enum_selection = SelectField("Enum Selection", **enum_field_options(EnumClass))

    Labels are produced from enum_instance.__html__() or
    str(eum_instance), value strings with str(enum_instance).

    """
    assert not {'__str__', '__html__'}.isdisjoint(vars(enum)), (
        "The {!r} enum class does not implement a __str__ or __html__ method")

    def coerce(name):
        if isinstance(name, enum):
            # already coerced to instance of this enum
            return name
        try:
            return enum(name)
        except KeyError:
            raise ValueError(name)

    return dict(choices=[(v, v) for v in enum], coerce=coerce)


class TaxView(ModelView):
    _model = models.Tax


def range_formatter(view, value):
    lower, upper = value.lower, value.upper
    if lower is None:
        lower = '-Inf'
    if upper is None:
        upper = '+Inf'

    return "{0}{1}, {2}{3}".format(value._bounds[0], lower, upper, value._bounds[1])


MY_DEFAULT_FORMATTERS = dict(typefmt.BASE_FORMATTERS)
MY_DEFAULT_FORMATTERS.update({
    Range: range_formatter
})


class TaxConfigView(ModelView):
    _model = models.TaxConfig

    column_list = ['tax_config_id', 'tax.tax_code', 'service', 'tax_type', 'date_range', 'pretax_range', 'tax_value',
                   'weekdays_byte', 'included_in_rate']
    column_filters = ['tax_config_id', 'tax.tax_code', 'service_category_id', 'service.service_accounting_code']

    form_extra_fields = {
        'effective_date': DateField('Effective Date'),
        'expiry_date': DateField('Expiry Date (Exclusive)', validators=[Optional()]),
        'pretax_from': DecimalField('Pretax From', validators=[Optional()]),
        'pretax_to': DecimalField('Pretax To (Exclusive)', validators=[Optional()]),
    }

    form_excluded_columns = ['hotel_tax_configs', 'seller_tax_configs']

    column_type_formatters = MY_DEFAULT_FORMATTERS

    form_overrides = {
        'tax_type': SelectField
    }

    form_args = {
        'tax_type': enum_field_options(TaxType)
    }

    # Model handlers
    def create_model(self, form):
        flask.g.date_range = DateRange(form._fields.pop('effective_date').data, form._fields.pop('expiry_date').data)
        flask.g.pretax_range = NumericRange(form._fields.pop('pretax_from').data, form._fields.pop('pretax_to').data)
        try:
            model = super(TaxConfigView, self).create_model(form)
        except Exception as ex:
            flash(gettext('Failed to create record. %(error)s', error=str(ex)), 'error')
            logger.exception("Failed to create record")
            self.session.rollback()
            return False
        return model

    def update_model(self, form, model):
        flask.g.date_range = DateRange(form._fields.pop('effective_date').data, form._fields.pop('expiry_date').data)
        flask.g.pretax_range = NumericRange(form._fields.pop('pretax_from').data, form._fields.pop('pretax_to').data)
        try:
            updated = super(TaxConfigView, self).update_model(form, model)
        except Exception as ex:
            flash(gettext('Failed to update record. %(error)s', error=str(ex)), 'error')
            logger.exception("Failed to update record")
            self.session.rollback()
            return False
        return updated

    def on_model_change(self, form, model, is_created):
        model.date_range = flask.g.date_range
        model.pretax_range = flask.g.pretax_range
        model.tax_type = model.tax_type.value


class HotelTaxConfigMappingView(ModelView):
    _model = models.HotelTaxConfigMapping

    column_list = ['hotel', 'tax_config']
    column_filters = ['hotel.cs_hotel_id', 'hotel.hotel_name', 'tax_config.tax.tax_code', 'tax_config.service_category_id']
    column_formatters = {
        'hotel': lambda v, c, m, p: Markup("<a href={0}>{1}</a>".format(url_for('hotelconfig.edit_view', id=m.hotel.id),
                                                                        repr(m.hotel))),
        'tax_config': lambda v, c, m, p: Markup("<a href={0}>{1}</a>".format(url_for('taxconfig.edit_view',
                                                                                     id=m.tax_config.tax_config_id),
                                                                             repr(m.tax_config)))
    }


class SellerTaxConfigMappingView(ModelView):
    _model = models.SellerTaxConfigMapping

    column_list = ['seller', 'tax_config']


class SellerModelView(ModelView):
    _model = models.Seller
    can_create = False
    can_delete = False

    column_list = ['seller_id', 'name', 'category', 'state_id']


class TaxableAmountRuleView(ModelView):
    _model = models.TaxableAmountRule
    can_create = True
    can_delete = True

    column_list = ['rule_id', 'service', 'percentage_of_total_value', 'minimum_taxable_amount']
    column_filters = ['rule_id', 'service_category_id', 'service.service_accounting_code']


class HotelTaxableAmountRuleMappingView(ModelView):
    _model = models.HotelTaxableAmountRuleMapping

    can_create = True
    can_delete = True

    column_list = ['hotel', 'taxable_amount_rule']
    column_filters = ['hotel.cs_hotel_id', 'hotel.hotel_name', 'taxable_amount_rule.rule_id',
                      'taxable_amount_rule.service_category_id']

    column_formatters = {
        'hotel': lambda v, c, m, p: Markup("<a href={0}>{1}</a>".format(url_for('hotelconfig.edit_view', id=m.hotel.id),
                                                                        repr(m.hotel))),
        'taxable_amount_rule': lambda v, c, m, p: Markup(
            "<a href={0}>{1}</a>".format(url_for('taxableamountrule.edit_view',
                                                 id=m.taxable_amount_rule.rule_id),
                                         repr(m.taxable_amount_rule)))
    }
