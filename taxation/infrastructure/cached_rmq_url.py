from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient

tenant_wise_cached_rmq_url = dict()


def read_and_store_rmq_url():
    for tenant in TenantClient.get_active_tenants():
        tenant_wise_cached_rmq_url[tenant.tenant_id] = AwsSecretManager.get_rmq_url(tenant_id=tenant.tenant_id)


def get_rmq_url(tenant_id):
    if not tenant_wise_cached_rmq_url:
        read_and_store_rmq_url()

    return tenant_wise_cached_rmq_url.get(tenant_id)
