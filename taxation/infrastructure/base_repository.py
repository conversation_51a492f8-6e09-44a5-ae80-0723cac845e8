from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from taxation.infrastructure import cache


class BaseRepository(object):
    _model = None
    KEY_PREFIX = 'taxation'

    """
    Does not commit/rollback. Transaction lifetime/scope to be determined at
    higher levels
    """

    @classmethod
    def build_cache_key(cls, cache_key):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        return "{tenant_id}.{key_prefix}.{cache_key}".format(tenant_id=tenant_id, key_prefix=cls.KEY_PREFIX,
                                                             cache_key=cache_key)

    @classmethod
    def remove_from_cache(cls):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

        cache.get_cache().delete(
            "{tenant_id}.{key_prefix}.{cache_key}".format(tenant_id=tenant_id, key_prefix=cls.KEY_PREFIX,
                                                          cache_key=cls.CACHE_KEY))

    @staticmethod
    def session():
        return db_engine.get_session()

    def create(self, item):
        self.session().add(item)
        self.session().commit()
        return item

    def expunge(self, items):
        """
        Use this method if you want a bunch of objects produced by querying a session
        to be usable outside the scope of the session
        Not using this will result in error:
            sqlalchemy.orm.exc.DetachedInstanceError: Instance is not bound to a Session; attribute refresh operation
            cannot proceed # noqa
        http://stackoverflow.com/questions/8253978/sqlalchemy-get-object-not-bound-to-a-session
        """
        for item in items:
            self.session().expunge(item)

    def update(self):
        self.session().commit()

    def create_all(self, items):
        self.session().add_all(items)
        self.session().commit()
        return items


class BaseCachingRepository(object):
    KEY_PREFIX = 'taxation'

    @classmethod
    def build_cache_key(cls, cache_key):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        return "{tenant_id}.{key_prefix}.{cache_key}".format(tenant_id=tenant_id, key_prefix=cls.KEY_PREFIX,
                                                             cache_key=cache_key)

    @classmethod
    def remove_from_cache(cls):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        cache.get_cache().delete("{tenant_id}.{key_prefix}.{cache_key}".format(tenant_id=tenant_id,
                                                                               key_prefix=cls.KEY_PREFIX,
                                                                               cache_key=cls.CACHE_KEY))
