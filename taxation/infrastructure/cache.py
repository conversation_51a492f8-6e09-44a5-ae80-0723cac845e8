import logging

import redis
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.exceptions import InvalidTenantException
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from taxation.domain.tax import repo_provider

logger = logging.getLogger(__name__)


def refresh_redis_cache():
    repo_provider.hotel_config_repository.remove_from_cache()
    repo_provider.hotel_config_repository.refresh_cache()

    repo_provider.gst_repository.remove_from_cache()
    repo_provider.gst_repository.refresh_cache()

    repo_provider.service_repository.remove_from_cache()
    repo_provider.service_repository.refresh_cache()

    repo_provider.room_config_repository.remove_from_cache()
    repo_provider.room_config_repository.refresh_cache()

    repo_provider.sku_mapping_repository.remove_from_cache()
    repo_provider.sku_mapping_repository.refresh_cache()


def create_cache_configs(host, port, password, db):
    cache_configs = dict(host=host, port=port, db=db, password=password, charset="utf-8", decode_responses=True)
    return cache_configs


tenant_wise_caches = dict()


def get_redis_credentials(tenant_id=None):
    redis_configs = dict()
    active_tenants = TenantClient.get_active_tenants()

    if tenant_id:
        active_tenants = [tenant for tenant in active_tenants if tenant.tenant_id == tenant_id]

    for tenant in active_tenants:
        tenant_id = tenant.tenant_id
        redis_creds = AwsSecretManager.get_redis_credentials(tenant_id)
        redis_configs[tenant_id] = create_cache_configs(redis_creds['host'], redis_creds['port'],
                                                        redis_creds['password'], redis_creds['db'])

    return redis_configs


def setup_tenant_cache(tenant_id=None):
    """
    Setup a tenant wise StrictRedis objects. The setup should be done only once, on application startup. So this
    method is called on at module level.

    To access tenant cache, get the redis_cache for that specific tenant from the created tenant_wise_caches

    :return:
    """
    redis_configs = get_redis_credentials(tenant_id=tenant_id)
    if tenant_id:
        redis_config = redis_configs.get(tenant_id)
        if not redis_config:
            raise InvalidTenantException()

        tenant_wise_caches[tenant_id] = redis.StrictRedis(**redis_config)
    else:
        for tenant_id, redis_config in redis_configs.items():
            if tenant_id in tenant_wise_caches:
                continue
            tenant_wise_caches[tenant_id] = redis.StrictRedis(**redis_config)


def get_cache(tenant_id=None):
    if not tenant_id:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

    cache = tenant_wise_caches.get(tenant_id)
    if not cache:
        setup_tenant_cache(tenant_id=tenant_id)
        cache = tenant_wise_caches.get(tenant_id)
        if not cache:
            raise InvalidTenantException()

    return cache
