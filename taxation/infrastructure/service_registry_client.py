import enum
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry



class ServiceEndPointNames(enum.Enum):
    CATALOG_SERVICE_URL = "catalog_service_url"
    NOTIFICATION_SERVICE_URL = "notification_service_url"


class ServiceRegistryClient:

    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_notification_service_url(cls):
        service_name = ServiceEndPointNames.NOTIFICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
