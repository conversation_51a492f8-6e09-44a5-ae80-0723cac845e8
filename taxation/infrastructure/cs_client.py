import json
import logging

from flask import current_app

from taxation.infrastructure.base_client import BaseClient

logger = logging.getLogger(__name__)


class CatalogingServiceApiClient(BaseClient):
    def __init__(self):
        self.sku_url = current_app.config.get('CATALOGING_SERVICE_GET_SKU_URL')
        self.all_sku_url = current_app.config.get('CATALOGING_SERVICE_GET_ALL_SKU_URL')
        self.activate_sku_url = current_app.config.get('CATALOGING_SERVICE_ACTIVATE_SKU_URL')
        self.property_skus_url = current_app.config.get('CATALOGING_SERVICE_PROPERTY_SKUS_URL')
        self.tenant_config_url = current_app.config.get('CATALOGING_SERVICE_GET_CONFIG_URL')

    def get_sku_from_sku_code_list(self, sku_code_list):

        sku_code_string = ','.join(sku_code_list)

        url = self.sku_url.format(sku_code_string)

        return self.get(url)

    def get_sku_from_sku_code(self, sku_code):

        url = self.sku_url.format(sku_code)

        return self.get(url)

    def get_all_sku(self):

        return self.get(self.all_sku_url)

    def activate_sku(self, data):
        data = json.dumps(data)
        return self.post(self.activate_sku_url, data=data, headers={'Content-Type': 'application/json'})

    def get_property_skus(self, cs_hotel_id):
        return self.get(self.property_skus_url.format(cs_hotel_id))

    def get_tenant_config(self):
        return self.get(self.tenant_config_url)
