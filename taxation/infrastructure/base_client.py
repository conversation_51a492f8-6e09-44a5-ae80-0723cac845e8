import logging
import json

import requests
from flask import current_app
from requests import RequestException
from requests.adapters import HTTPA<PERSON>pter
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request
from urllib3 import Retry

logger = logging.getLogger(__name__)


class BaseClient:

    @staticmethod
    def http_default():
        headers = {
            "Content-Type": "application/json"
        }
        enrich_outgoing_request(headers)
        session = requests.Session()
        retries = Retry(total=3, backoff_factor=0.1, status_forcelist=[502, 503, 504, 499])
        # default pool_connections and pool_maxsize is 10, will go ahead with this only. if needed, can be increased
        session.mount('http://', HTTPAdapter(max_retries=retries))
        session.mount('https://', HTTPAdapter(max_retries=retries))
        return headers, session

    def get(self, url, headers=None, **kwargs):
        default_header, session = self.http_default()
        headers = headers if headers else default_header
        try:
            response = session.get(url=url, headers=headers, **kwargs)

        # RequestException captures all possible request(http) related error.
        except RequestException as re:
            message = "API failure: Url: %s, Params: %s, RequestException: %s" % (url, str(kwargs), repr(re))
            logger.exception(message)
            return None
        logger.info("rest_client get call response, data: %s" % str(response))
        # default positive check
        if response.status_code in [200, 201, 202]:
            return response.json()

        message = "Module: %s, Function: %s, API non-positive status code, Url: %s, Status_code: %s, Response data: " \
                  "%s, " \
                  "params: %s" % (__name__, "get", url, response.status_code, response.text, str(kwargs))
        logger.error(message)
        return None

    def post(self, url, headers=None, data=None, json=None, **kwargs):
        default_header, session = self.http_default()
        headers = headers if headers else default_header
        try:
            response = session.post(url=url, headers=headers, data=data, json=json, **kwargs)

        # RequestException captures all possible request(http) related error.
        except RequestException as re:
            message = "API failure: Url: %s, Params: %s, RequestException: %s" % (url, str(kwargs), repr(re))
            logger.exception(message)
            return None

        # default positive check
        if response.status_code in [200, 201, 202]:
            return response.json()

        message = "Module: %s, Function: %s, API non-positive status code, Url: %s, Status_code: %s, Response data: " \
                  "%s, " \
                  "params: %s" % (__name__, "get", url, response.status_code, response.text, str(kwargs))
        logger.error(message)
        return None
