import logging
import os
import flask

from treebo_commons.request_tracing.flask import before_request, after_request
from taxation.infrastructure.service_registry_client import ServiceRegistryClient
from taxation.middleware import before_request as tax_before_request


class BaseConfig(object):
    DEBUG = False
    TESTING = False
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    # SQLALCHEMY_ECHO=False
    SECRET_KEY = 'q_\xdd\x1c\xbd\x15\xeb\xdb\x8dD5\xc8\xfcR\x84\xd8?\xc5\x03rC=\x12\x98'
    ENVIRONMENT = 'local'
    LOG_FORMAT = '%(asctime)s:%(name)s:%(levelname)s:%(message)s'
    LOG_LEVEL = logging.DEBUG
    REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
    REDIS_PORT = os.environ.get('REDIS_PORT', '6379')
    REDIS_DB_NUMBER = os.environ.get('REDIS_DB_NUMBER', '1')

    BROKER_URL = 'redis://' + REDIS_HOST + ':' + REDIS_PORT + '/' + REDIS_DB_NUMBER
    CELERY_RESULT_BACKEND = BROKER_URL

    #     DT errors slack channel
    SLACKBOT_HOOK = os.environ.get('SLACKBOT_HOOK',
                                   '*****************************************************************************')
    CONFIGS_SLACKBOT_HOOK = SLACKBOT_HOOK
    BASE_DIR = os.path.dirname(os.path.dirname(__file__))
    MAIL_SERVER = 'email-smtp.eu-west-1.amazonaws.com'
    MAIL_PORT = 587
    MAIL_USERNAME = 'AKIAIH5CFGSGIVOGDTAA'
    MAIL_PASSWORD = 'Al3TbfkFvoZFpPxPaIm4i6T5q/ZJHOsW9IKSCYm01E8L'
    MAIL_USE_TLS = True
    MISSING_DECLARED_TARIFF_REPORT_MAIL_LIST = ['<EMAIL>']
    SQLALCHEMY_DATABASE_URI = os.environ.get('DB', 'postgresql://localhost:5432/taxation')
    SQLALCHEMY_POOL_SIZE = int(os.environ.get('DB_CONNECTION_POOL_SIZE', 2))
    LOG_ROOT = os.environ.get('LOG_ROOT', '/var/log/taxation/')

    CATALOGING_SERVICE_BASE_URL = "" if os.environ.get('APP_ENV')=='testing' else \
        ServiceRegistryClient.get_catalog_service_url() + "/"
    CATALOGING_SERVICE_URL_PREFIX = os.environ.get('CATALOGING_SERVICE_URL_PREFIX', 'cataloging-service/api/v1/')
    CATALOGING_SERVICE_GET_SKU_URL = CATALOGING_SERVICE_BASE_URL + CATALOGING_SERVICE_URL_PREFIX + 'sku/?codes={0}'
    CATALOGING_SERVICE_GET_ALL_SKU_URL = CATALOGING_SERVICE_BASE_URL + CATALOGING_SERVICE_URL_PREFIX + 'sku/'
    CATALOGING_SERVICE_ACTIVATE_SKU_URL = CATALOGING_SERVICE_BASE_URL + CATALOGING_SERVICE_URL_PREFIX + \
                                          'activation/sync/'
    CATALOGING_SERVICE_PROPERTY_SKUS_URL = CATALOGING_SERVICE_BASE_URL + 'cataloging-service/api' + \
                                           '/properties/{0}/sku/'

    CATALOGING_SERVICE_GET_CONFIG_URL = CATALOGING_SERVICE_BASE_URL + 'cataloging-service/api' + \
                                           '/v1/tenant-configs'

    CATALOGING_RABBITMQ_URL = os.environ.get('CATALOGING_RABBITMQ_URL',
                                             'amqp://rms:<EMAIL>:5672/catalog')

    TAX_RABBITMQ_HOST = os.environ.get('TAX_RABBITMQ_HOST', 'localhost')

    RMQ_PUBLISHER = {
        'dt_publish': {
            'RMQ_EXCHANGE': os.environ.get('TAX_DT_RMQ_EXCHANGE', 'tax_service_rmq_exchange'),
            'ROUTING_KEY': os.environ.get('TAX_DT_RMQ_ROUTING_KEY', 'tax_rmq_dt_key')
        },
        'dead_letter': {
            # this is the dead-letter exchange
            'RMQ_EXCHANGE': os.environ.get('TAX_X_RMQ_EXCHANGE', 'tax_service_rmq_x_exchange')
        }
    }
    RMQ_PREFETCH_COUNT = int(os.environ.get('RMQ_PREFETCH_COUNT', '10'))
    RMQ_RETRY_COUNT = int(os.environ.get('RMQ_RETRY_COUNT', '10'))
    RMQ_CONSUMER_CONFIGS = {
        'cataloging_service_category_sync': {
            "url": CATALOGING_RABBITMQ_URL,
            "exchange_name": 'cs_exchange',
            "exchange_type": 'topic',
            "queue_name": 'tax_service_sync_queue',
            "queue_routing_key": 'com.cs.sku.category'
        },
        'cataloging_service_hotel_sync': {
            "url": CATALOGING_RABBITMQ_URL,
            "exchange_name": 'cs_exchange',
            "exchange_type": 'topic',
            "queue_name": 'tax_hotel_sync_queue',
            "queue_routing_key": 'com.cs.property'
        },
        'cataloging_service_room_sync': {
            "url": CATALOGING_RABBITMQ_URL,
            "exchange_name": 'cs_exchange',
            "exchange_type": 'topic',
            "queue_name": 'tax_room_sync_queue',
            "queue_routing_key": 'com.cs.property.room'
        }
    }

    # Middlewares
    WSGI_MIDDLEWARES = []
    BEFORE_REQUEST_MIDDLEWARES = [lambda: before_request(flask.request), tax_before_request]
    AFTER_REQUEST_MIDDLEWARES = [lambda resp: after_request(resp, flask.request)]

    ROOM_NAMES = os.environ.get('ROOM_TYPES', 'oak,maple,mahogany,acacia')
    ROOM_NAME_LIST = list()
    room_names = ROOM_NAMES.split(',')
    for room_name in room_names:
        ROOM_NAME_LIST.append(room_name.strip().lower())

    PRICE_BASED_COMPUTE_START_DATE = os.environ.get('PRICE_BASED_COMPUTE_START_DATE', '2018-08-01T03:00:00+0530')


class DevelopmentConfig(BaseConfig):
    DEBUG = True
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    ENVIRONMENT = 'development'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DB', 'postgresql://rohitjain@localhost:5432/taxation')
    # SQLALCHEMY_ECHO = True


class StagingConfig(BaseConfig):
    DEBUG = True
    LOG_ROOT = os.environ.get('LOG_ROOT', '/var/log/taxation/')
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    ENVIRONMENT = 'staging'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DB', 'postgresql://treebo:treebo@localhost:5432/taxation')


class PerformanceConfig(BaseConfig):
    DEBUG = False
    ENVIRONMENT = 'performance'


class TestingConfig(BaseConfig):
    TESTING = True
    ENVIRONMENT = 'development'
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    LOG_ROOT = os.environ.get('LOG_ROOT', '/var/log/taxation/')


class ProductionConfig(BaseConfig):
    LOG_LEVEL = logging.INFO
    ENVIRONMENT = 'production'
    LOG_ROOT = os.environ.get('LOG_ROOT', '/var/log/taxation/')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DB', 'postgresql://treebo:treebo@localhost:5432/taxation')
    #     DT(declared tariff) related errors
    SLACKBOT_HOOK = os.environ.get('SLACKBOT_HOOK',
                                   '*****************************************************************************')
    #     any tax related errors
    CONFIGS_SLACKBOT_HOOK = os.environ.get('CONFIGS_SLACKBOT_HOOK',
                                           '*****************************************************************************')
    MISSCATALOGING_SERVICE_GET_SKU_URLING_DECLARED_TARIFF_REPORT_MAIL_LIST = ['<EMAIL>',
                                                                              '<EMAIL>']
    CATALOGING_SERVICE_BASE_URL = os.environ.get('CATALOGING_SERVICE_BASE_URL', 'http://catalog.treebo.com/')
    CATALOGING_SERVICE_URL_PREFIX = os.environ.get('CATALOGING_SERVICE_URL_PREFIX', 'cataloging-service/api/v1/')
    CATALOGING_SERVICE_GET_SKU_URL = CATALOGING_SERVICE_BASE_URL + CATALOGING_SERVICE_URL_PREFIX + 'sku/?codes={0}'
    CATALOGING_SERVICE_GET_ALL_SKU_URL = CATALOGING_SERVICE_BASE_URL + CATALOGING_SERVICE_URL_PREFIX + 'sku/'


config = {
    "development": DevelopmentConfig,
    "staging": StagingConfig,
    "testing": TestingConfig,
    "default": BaseConfig,
    "production": ProductionConfig,
    "performance": PerformanceConfig
}


def get_config_class():
    environment = os.environ.get('APP_ENV', 'development')
    config_class = config[environment]
    return config_class()
