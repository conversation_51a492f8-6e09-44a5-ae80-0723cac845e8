#!/bin/bash

SOCKFILE=/var/run/gunicorn.sock  # we will communicte using this unix socket
echo "Starting $NAME as `whoami`"

# Create the run directory if it doesn't exist
RUNDIR=$(dirname $SOCKFILE)
echo $RUNDIR
test -d $RUNDIR || mkdir -p $RUNDIR

PROJECT_BASE=/usr/src/app
export PYTHONPATH=$PROJECT_BASE:$PYTHONPATH

echo "Starting Gunicorn"
newrelic-admin run-program gunicorn -c deployment_config/gunicorn_config.py manage:app

