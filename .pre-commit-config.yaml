-   repo: git://github.com/pre-commit/pre-commit-hooks
    sha: 96fb7fa10f2f4c11ed33482a9ad7474251e5e97f
    always_run: true
    hooks:
    -   id: trailing-whitespace
    -   id: check-added-large-files
    -   id: check-merge-conflict
    -   id: debug-statements
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: flake8
        args:
        - --max-line-length=120
        - --show-source
        - --statistics
        - --exclude=*/__init__.py,*/migrations/*.py
        stages: [commit]
-   repo: local
    hooks:
    -   id: pylint
        name: pylint
        entry: python3 -m pylint.__main__
        language: system
        files: \.py$
        stages: [commit]
