#!/usr/bin/env bash
# This is a randomish md5 to identify this script
# 138fd403232d2ddd5efb44317e38bf03

pushd `dirname $0` > /dev/null
HERE=`pwd`
popd > /dev/null

retv=0
args=""
ENV_PYTHON_RETV=1

which pre-commit >& /dev/null
WHICH_RETV=$?
python3 -c 'import pre_commit.main' >& /dev/null
PYTHON_RETV=$?


if ((
        (WHICH_RETV != 0) &&
        (PYTHON_RETV != 0)
)); then
    echo '`pre-commit` not found.  Did you forget to activate your virtualenv?'
    exit 1
fi


# Run the legacy pre-commit if it exists
if [ -x "$HERE"/pre-commit.legacy ]; then
    "$HERE"/pre-commit.legacy
    if [ $? -ne 0 ]; then
        retv=1
    fi
fi



# Run pre-commit
if ((WHICH_RETV == 0)); then
    pre-commit $args
    PRE_COMMIT_RETV=$?
elif ((ENV_PYTHON_RETV == 0)); then
    "$ENV_PYTHON" -m pre_commit.main $args
    PRE_COMMIT_RETV=$?
else
    python3 -m pre_commit.main $args
    PRE_COMMIT_RETV=$?
fi

if ((PRE_COMMIT_RETV != 0)); then
    retv=1
fi

`./coverage.sh`
if [[ $? == 0 ]]
then
	echo "> Tests passed !"
else
	echo "> Tests DID NOT pass !"
	retv=1
fi

exit $retv
