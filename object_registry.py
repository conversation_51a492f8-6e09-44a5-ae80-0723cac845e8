instances = dict()
event_handlers = dict()
instances_to_register = []
event_handlers_to_register = dict()


def locate_instance(clazz):
    return instances.get(clazz)


def complete_instance_creation():
    for instance_to_register in instances_to_register:
        cls, dependencies, arguments = instance_to_register
        args = []
        if dependencies:
            for d in dependencies:
                args.append(locate_instance(d))

        if arguments:
            for arg in arguments:
                args.append(arg)

        instances[cls] = cls(*args)

    for event_type, cls in event_handlers_to_register.items():
        event_handlers[event_type] = locate_instance(cls)


def finalize_app_initialization(app):
    with app.app_context():
        complete_instance_creation()
