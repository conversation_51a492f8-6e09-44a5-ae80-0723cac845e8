cffi==1.11.4
cryptography==2.1.4

Werkzeug==0.16.0
Flask==0.12
Flask-Admin==1.5.6
flask-marshmallow==0.7.0
Flask-Migrate==2.0.2
marshmallow==2.13.5
marshmallow-sqlalchemy==0.12.1
SQLAlchemy==1.3.2
voluptuous==0.9.3

psycopg2==2.7
gevent==21.12.0
gunicorn==19.8.0
psycogreen==1.0
pytz==2016.10
requests==2.18.4

# Generating GraphViz dot files
sadisplay==0.4.6
# Convert dot files to png file
graphviz==0.5.2

celery==4.1.0

# Used by Flask jsonify internally instead of json
# Has proper encoders for Decimal type
simplejson==3.17.0

logstash-formatter==0.5.16

# Cache
redis==2.10.5
hiredis==0.2.0

# Documentation
Sphinx==1.5.2
sphinxcontrib-httpdomain==1.5.0

newrelic==4.20.1.121

kombu==4.1.0

nested_dict==1.61
Flask-Mail==0.9.1

# WTForms(wtforms)
WTForms==2.1

# fabric
#Fabric==1.14.0
apispec==0.36.0
# Freezing because flasgger does not have upper bound. And jsonschema 4.0.0, doesn't support python 3.5
jsonschema==3.2.0
flasgger==0.8.0

# pytest
pytest==6.1.0
pytest-html==1.22.1
xlrd==1.1.0
