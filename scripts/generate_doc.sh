#!/bin/bash
exclude_path=(deployment_config tests docs scripts */migrations* */config* */constants* */settings* */app.py */extensions.py */decorators.py */admin_views* */schemas.py */serializers.py)

# Uncomment and run the below 4 commands (sphinx-apidoc & python script) when the module structure of the project changes.
# They will generate the appropriate .rst files.
# After running them:
#   - Modify the `docs/index.rst` file to change `taxation` to `taxation/taxation`
#   - Modify the newly generated docs/conf.py file to add `add_module_names = False` at the end of the file.

# sphinx-apidoc --force -F --implicit-namespaces -o docs -H Taxation -A "Treebo" taxation ${exclude_path[@]}
# python3 scripts/remove_submodules_section_from_rst.py docs/rst

# Run the below substitution on all the rst files generated, to remove fully-qualitified module name
# :%s/^\%([a-zA-Z0-9]*[.]\)*\([a-zA-Z0-9_]\+\) \(module\)/\1 \2/g

sphinx-build -b html docs docs/_build
