# README #

This repository contains the source code for - `Taxation`.

* The design document for the same can be found here: [Taxation Design Document](https://treebo.atlassian.net/wiki/display/EN/Tax+Service)
* API document available here: [Taxation API Document](https://treebo.atlassian.net/wiki/display/EN/Tax+API+Doc)
* Database Schema diagram available here: [Database Schema](https://treebo.atlassian.net/wiki/display/EN/Taxation+DB+Schema)

This README file contains the steps to setup the `taxation` application on local machine using Docker.

1. Deployment using Docker and fabric:
--------------------------------------
a). Deploying on local machine:
-------------------------------
To deploy taxation on local machine on Docker, follow the below steps:

# Setup Fabric:
* Fabric doesn't work with Python 3. So, create a virtualenv with Python 2 version:
```bash
virtualenv ~/.venvs/fabric_env
```
* Activate virtualenv:
```bash
source ~/.venvs/fabric_env/bin/activate
# Install Fabric
pip install Fabric
```
* Modify log_root settings in .fabricrc file, and copy `.fabricrc` file to your home:
```bash
cp .fabricrc ~
```
* Run the below command to check that Fabric is setup. It should print "Hello There!!" to console:
```bash
fab test_fabric
```

# Create a network:
```bash
fab localhost create_network:network='network_taxation'
```

# Setup Postgres:
```bash
fab localhost setup_postgres:network='network_taxation',postgres_user='treebo',postgres_pwd='treebo',postgres_db='taxation'
```
The above command sets up postgres container (running on port 5432), and creates the above ROLE (treebo), given password, and database - 'taxation'


# Check that docker containers are running for Postgres and RabbitMQ, and see they are connected to unirate network:
```bash
docker ps
docker network inspect network_taxation
```
You should see the 1 container in the "Containers" list with name: `postgres`

# Setting up application:
Pre-requisite:

* Change the Database Configuration in taxation/settings.py file DevelopmentConfig as per the above parameters. Keep the host as `postgres` (The name of the above container)

Command:
```bash
fab localhost deploy:tag_name='taxation',version='v1.0',port='8001',network='network_taxation'
```

Explanation:

* The above command will create a docker image with name `taxation:v1.0`
* It will start 1 docker container for gunicorn (with the name: `taxation_gunicorn`), and connect them to `network_taxation` network
* It will run the migration on the postgres container database
* Check that application is setup, by visiting: `localhost:8001/taxation/admin`


2. How to run Tests:
-------------------
Go the the project root, and run the coverage script:
```bash
cd /path/to/project
./coverage.sh
```

The above command will run the tests, and generate coverage report in `htmlcov/` directory under project path.
To check the report, run Python SimpleHTTPServer:
```bash
cd /path/to/project/
python3 -m http.server 8001
```
After above command, go the url: `localhost:8001/htmlcov` to view the test coverage report.


3. Creating Model Diagram:
--------------------------
Steps to generate the schema diagram as found in the document linked above:

* Create graphviz dot file:
```bash
python3 graph_model.py
```

* Convert png file from dot file:
```bash
dot -Tpng schema.dot > schema.png
```
